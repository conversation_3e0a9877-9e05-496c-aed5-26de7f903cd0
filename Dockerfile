# Use the official Python base image
FROM python:3.8

# Copy the application code
COPY src/ app/

# Set the working directory
WORKDIR /app

# Create a virtual environment and activate it
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip in the virtual environment and install dependencies
RUN pip install --upgrade pip
RUN pip install -r requirements.txt

# Set the environment variable for the port
ENV PORT 8080

# Set the command to run your application using gunicorn within the virtual environment
CMD exec /opt/venv/bin/gunicorn --bind :$PORT --workers 1 --threads 8 run:app --preload