import pyrebase
from flask import Flask
import re

from competencyTest.config import *

import re

def regex_replace(s, pattern, replacement):
    return re.sub(pattern, replacement, s)


app = Flask(__name__)
app.jinja_env.filters['regex_replace'] = regex_replace
app.secret_key = FLASK_SECRET_KEY
firebase = pyrebase.initialize_app(FIREBASE_CONFIG)

pyre_auth = firebase.auth()
db = firebase.database()

from competencyTest import routes