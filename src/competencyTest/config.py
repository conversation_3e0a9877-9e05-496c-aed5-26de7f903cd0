#%%
import os

# Get the current module's directory
module_dir = os.path.dirname(__file__)

# Define the relative path to the CSV file
csv_file = 'dept_and_unit.csv'
app_creds = 'competencyTestCredentials.json'
icon_file = 'tkoh.ico'

# Construct the absolute path by joining the module directory and the relative path
csv_file_path = os.path.join(module_dir, csv_file)
cred_file_path = os.path.join(module_dir, app_creds)
icon_file_path = os.path.join(module_dir, 'static',icon_file)

BASIC_APP_CONFIG = {
  'name': 'TKOH Competency Assessment',
  'retriever': '<EMAIL>',
  'default_expiry': 3
}

FIREBASE_CONFIG = {
  "apiKey": "AIzaSyCDUO4sEPvrNx7sZ-SXLvCr8pRRnXSo6vw",
  "authDomain": "competency-test-66446.firebaseapp.com",
  "databaseURL": "https://competency-test-66446-default-rtdb.asia-southeast1.firebasedatabase.app",
  "projectId": "competency-test-66446",
  "storageBucket": "competency-test-66446.appspot.com",
  "messagingSenderId": "1078661949013",
  "appId": "1:1078661949013:web:e765a2389f31526145d6dc",
  "measurementId": "G-DX6ER09LTH"
}

FLASK_SECRET_KEY = 'tkohnsd'

RETRIEVER = '<EMAIL>'


ADMIN_PERMISSION = [
    {
      'email':'<EMAIL>',
      'dept': 'NSD',
      'unit': 'NSD',
      'access': ['ALL']
    },
    {
      'email':'<EMAIL>',
      'dept': 'NSD',
      'unit': 'NSD OFFICE',
      'access': ['ALL']
    }
]

RANKS = [
    'DOM / SNO / NC', 
    'WM / ANC', 
    'APN / NO', 
    'RN', 
    'EN'
]

ORGANIZER_NAMES = {
    'tutor': '指導員姓名',
    'observer': '觀察員姓名',
    'assessor': '評核員姓名'
}

""" fall_test_config = {
    'total_score': 10,
    'pass_score': 8,
    'score_per_question': 1
}
"""

""" 
fall_test_form = [
    {
        'header': {
            'title': f'Fall Prevention Quiz for Nurses {date.today().year}',
            'content': ''
        },
        'question': [
            {
                'id':'0',
                'q':'In this test, there are 10 questions in total. <br/>Each correct answer bears 1 mark, and the passing mark for this test is 8. <br/>Please choose the most appropriate answer.',
                'opt':[],
                'ans': ''
            }
        ]
    },
    {
        'header':{
            'title': 'MC Quiz',
            'content': ''
        },
        'question': [
            {
                'id':'1',
                'q':'Which condition(s) should <u>NOT</u> be defined as fall incident?',
                'opt':[
                    'Patient slide from the wheelchair to the floor with buttock landed',
                    'Patient felt dizziness when rising up from his bed and then sit on the floor to rest',
                    'Patient recognized his weakness and sat down intentionally while putting on trousers in standing position',
                    'Patient suddenly developed lower limb weakness when getting off from his bed and fell at bedside'
                ],
                'ans': 'Patient slide from the wheelchair to the floor with buttock landed'
            },
            {
                'id':'2',
                'q':'Which of the following variables is <u>NOT</u> a risk factor to be assessed in Morse Fall Scale?',
                'opt':[
                    'Secondary diagnosis',
                    'Body weight',
                    'Mental status',
                    'History of fall'
                ],
                'ans': 'Body weight'
            },
            {
                'id':'3',
                'q':'Which of the following factor will increase the fall risk of patient?',
                'opt':[
                    'Patient has more than one diagnoses and multiple medications used',
                    'Patient understands his own limitation of ambulation',
                    'Patient is in a bed rest and does not get out of bed at all',
                    'Patient walks with head erect, arm swinging freely at the side and striding without hesitant'
                ],
                'ans': 'Patient has more than one diagnoses and multiple medications used'
            },
            {
                'id':'4',
                'q':'Under which of the following condition, would you need to reassess patient’s risk of fall?',
                'opt':[
                    'Patient is back from procedure with sedation given',
                    'Patient has poor appetite since yesterday ONLY',
                    'Patient with MFS=15 has been admitted to ward over 10 hours',
                    'Wound dressing is done to a patient at bedside'
                ],
                'ans': 'Patient is back from procedure with sedation given'
            },
            {
                'id':'5',
                'q':'When fall incident occurred, what should you do?',
                'opt':[
                    'Apply safety vest immediately before assessing patient’s condition',
                    'Perform physical examination, check and monitor vital sign and document properly',
                    'Assess the patient and inform doctor only if he complains pain over the body',
                    'Arrange patient’s bed away from the nursing station'
                ],
                'ans': 'Perform physical examination, check and monitor vital sign and document properly'
            },
            {
                'id':'6',
                'q':'What is the score in Morse Fall Scale indicating HIGH fall risk?',
                'opt':[
                    '25',
                    '35',
                    '40',
                    '>50'
                ],
                'ans': '>50'
            },
            {
                'id':'7',
                'q':'Which of the following is <u>NOT</u> an appropriate intervention for the patient with MFS score 25-50?',
                'opt':[
                    'Post up alert signage',
                    'Provide call bell and encourage to ask for help',
                    'Lower the bed height and set personal items within arm reach',
                    'Apply physical restraint immediately the prevention patient fall',
                    'Ensure proper footwear'
                ],
                'ans': 'Apply physical restraint immediately the prevention patient fall'
            },
            {
                'id':'8',
                'q':'Which of the following are appropriate intervention(s) for the patient with MFS score > 50?<br/>1. Arrange bed for easy observation<br/>2. Raise up bed side rails<br/>3. Put on safety vest and limb holders immediately before notifying the doctors and patient’s relatives<br/>4. Refer PT and OT for fall prevention program<br/>5. Offer special elimination round',
                'opt':[
                    '(1) & (2) only',
                    '(1), (2) & (3) only',
                    '(1), (2), (4) & (5) only',
                    'All of the above'
                ],
                'ans': '(1), (2), (4) & (5) only'
            },
            {
                'id':'9',
                'q':'A right hemiplegia patient was admitted for post-CVA epilepsy. <br/>He was fully alert on admission. <br/>He has HT, hyperlipidemia and DM and walked with stick. <br/>A heparin block was cannulated on his left hand for medication. <br/>He was advised bed rest and call bell was given. <br/>However, in the early morning, <br/>patient got out of bed and walked to the toilet by using furniture with unsteady gait <br/>and head down without informing ward staff. <br/>He suddenly developed seizure attack <br/>and fell on the floor at the corridor. <br/>What is the scoring in MFS for the above patient <br/>AFTER the fall incident?',
                'opt':[
                    '25',
                    '65',
                    '85',
                    '125'
                ],
                'ans': '125'
            },
            {
                'id':'10',
                'q':'Following to the previous scenario, what are the fall intervention(s) for the above patient?<br/>1. Raise up bed side rails<br/>2. Apply physical restraint immediately without notifying relatives<br/>3. Arrange special elimination round<br/>4. Rearrange bed near to the nursing station and apply fall alarm pad<br/>5. Refer allied health professionals for fall prevention program',
                'opt':[
                    '(1) & (3) only',
                    '(1), (2) & (3) only',
                    '(1), (3), (4) & (5) only',
                    'All of the above'
                ],
                'ans': '(1), (3), (4) & (5) only'
            }
        ]
    }
]
"""

#%%

""" def get_unit_list():

    df = pd.read_csv(csv_file_path)

    dept_unit = [
        {
          "email": f"{row['Unit'].strip().replace(' ', '-').lower()}@tkoh.org",
          "unit": row['Unit'].strip().upper(),
          "dept": row['Department'].strip().upper(),
          "access": row['Access'].strip().upper().split(',')
        } 
        for _, row in df.iterrows()
      ]

    return dept_unit """

# unit_permission = get_unit_list()

# %%
