from competencyTest import db
import re

def get_dept_and_ward(session, show_invisible=False):

    # Get the entire permission database
    permissions = dict(db.child("permission").get().val())

    # Extract the allowed departments for the current user
    allowed_departments = [
        data['access'] for data in permissions.values() 
        if data['email'] == session['user_email']
    ]

    allowed_departments = allowed_departments[0] if allowed_departments else []


    if 'ALL' in allowed_departments:

        if show_invisible:
            allowed_departments = list(set([
                data['dept'] 
                for data in permissions.values()
            ]))
        
        else:
            allowed_departments = list(set([
                data['dept'] 
                for data in permissions.values()
                if 'invisible' not in data
            ]))

    allowed_departments.sort()

    # Create the units_by_department dictionary
    units_by_department = {}
    if show_invisible:
        for data in permissions.values():
            units_by_department.setdefault(data['dept'], []).append(data['unit'])
    else:
        for data in permissions.values():
            if 'invisible' not in data.keys():
                units_by_department.setdefault(data['dept'], []).append(data['unit'])


    return allowed_departments, units_by_department

def get_all_depts_units():

    permissions = dict(db.child("permission").get().val())

    departments = list(set([
        data['dept'] for data in permissions.values()
        if 'invisible' not in data
    ]))

    departments.sort()

    units_by_department = {}
    for data in permissions.values():
        if 'invisible' not in data:
            units_by_department.setdefault(data['dept'], []).append(data['unit'])

    return departments, units_by_department
    
def get_tests_overview():

    tests = dict(db.child("test").get().val())
    tests_overview = {
        test_name: {
            test['info']['version']: {
                'title': test['content'].get('intro', {}).get('header', ''),
                'version': test['info']['version'],
                'version_name': test['info']['version_name'],
                'form': test['info']['form_type']
            }
            for test in test_versions.values()
        }
        for test_name, test_versions in tests.items()
    }


    return tests_overview

def get_latest_form(test_name):
    
    test_forms = dict(db.child('test').child(test_name).get().val())
    versions = list(test_forms.keys())
    versions.sort(reverse=True)
    latest_ver = versions[0]
    test_content = test_forms[latest_ver]['content']

    # reorder the keys
    test_content = {
        k: test_content.get(k, {})
        for k in ['intro', 'basic_entry', 'questions', 'additional_entry']
    }

    test_info = test_forms[latest_ver]['info']

    return test_content, test_info

def formulate_checklist(data, ver):
    print(data)
    new_checklist = {}
    new_checklist['info'] = {
        'test_name': data['test-name'].strip(),
        'form_type': 'checklist',
        'version': ver.strip(),
        'version_name': data['version-name'].strip(),
        'passing_criteria': data['passing-criteria'].strip(),
        'option_setting': data['option-setting'].strip()
    }

    new_checklist['content'] = {
    
        'intro':{
            'header': data['intro-header'].strip(),
            'details': data['intro-details'].strip()
        },
        'questions':{
            'header': data['questions-header'].strip(),
            'itemcol': data['col-item'].strip(),
            'details': {
                'items':{
                    f'q{int(k.split("-")[1]):02}' : {
                        'essential': True if data.get(f'item-essential-{k.split("-")[1]}', False) else False,
                        'text': v.strip()
                    }
                    for k,v in data.items() 
                    if k.startswith('item-') and '-essential' not in k
                },
                'options':{
                    f'o{int(k.split("-")[1]):02}' : {
                        'name': v.strip(),
                        'type': data[f'{k}-type'],
                        'count_score': True if data.get(f'cs-{k.split("-")[1]}', False) else False
                    }
                    for k,v in data.items() 
                    if k.startswith('ans-')
                    and not k.endswith('-type')
                }
            }
        }

    }

    new_checklist['content']['basic_entry'] = {
        'header': data.get('basic-entry-header','').strip(),
        'details': {}
    }
    new_checklist['content']['additional_entry'] = {
        'header': data.get('additional-entry-header','').strip(),
        'details': {}
    }
    for k,v in data.items():
        if k.startswith('new-basic-entry-input') or k.startswith('new-additional-entry-input'):
            prefix = k.split('-input')[0]
            idx = k[-2:]
            entry_name = prefix.replace('new-','').replace('-','_')
            entry_key = f'{entry_name}_{idx}'
            if data[f'{prefix}-select-{idx}'] == 'custom_options':
                new_checklist['content'][entry_name]['details'][entry_key] = {
                    'title': v.strip(),
                    'input': [x.strip() for x in data[f'{prefix}-select-{idx}-custom-options'].split('|')]
                }
            else:
                new_checklist['content'][entry_name]['details'][entry_key] = {
                    'title': v.strip(),
                    'input': data[f'{prefix}-select-{idx}']
                }
            input_required = k.replace('input','required')
            if input_required in data:
                new_checklist['content'][entry_name]['details'][entry_key]['required'] = True

    return new_checklist

def formulate_mc(data, ver):
    new_mc = {}
    new_mc['info'] = {
        'test_name': data['test-name'].replace(' ','_'),
        'form_type': 'mc',
        'version': ver,
        'version_name': data['version-name'],
        'pass_score': int(data['pass-score']),
        'total_score': int(data['total-score']),
        'score_per_question': int(data['score-per-q'])
    }

    pattern__n = r'_\d$'
    pattern_qXX = r'^q\d\d$'

    new_mc['content'] = {
    
        'intro':{
            'header': data['intro-header'],
            'details': data['intro-details']
        },
        'questions':{
            'header': data['questions-header'],
            'details': {
                k:{
                    'ans': data[f'{k}-ans'].strip(),
                    'id': k,
                    'opt':[
                        data[option].strip() for option in data.keys() 
                        if option.startswith(k)
                        and re.search(pattern__n, option)
                    ],
                    'q': v.strip()
                } 
                for k,v in data.items() 
                if re.match(pattern_qXX, k)
            }
                
        }

    }

    return new_mc


def check_key_contains_string(dictionary, target_string):
    for key in dictionary:
        if target_string in key:
            return True
    return False