import { dynamicUnitDropdown, alertMsgModal } from "./utils.js";

function getDataAttributes() {
    const dataContainer = document.getElementById('data-container');
    const unitsByDepartment = JSON.parse(dataContainer.dataset.unitsByDepartment);
    const allStaff = JSON.parse(dataContainer.dataset.allStaff);
    return { unitsByDepartment, allStaff};
}

function initialize() {
    const { unitsByDepartment, allStaff } = getDataAttributes();
    dynamicUnitDropdown('department','unit', unitsByDepartment);
    listenSubmitBtn(allStaff);
    listenConfirmBtn();
    listenFromTemp();
    listenDisciplineRadio();
}

function listenSubmitBtn(allStaff) {

    const submitBtn = document.getElementById('submit-btn');

    
    submitBtn.addEventListener('click', () => {

        const employeeNo = document.getElementById('employee-no');
        const employeeName = document.getElementById('employee-name');
        const employeeDepartment = document.getElementById('department');
        const employeeUnit = document.getElementById('unit');
        const employeeDiscipline = document.querySelector('input[name="employee_discipline"]:checked');
        const isUpdate = document.getElementById('update');
    
        const modalHeader = document.getElementById('confirm-add-staff-modal-header');
        const modalTitle = document.getElementById('confirm-add-staff-modal-label');
        const modalBodyTitle = document.getElementById('modal-body-title');
        const currNo = document.getElementById('current-employee-no');
        const cfmNo = document.getElementById('confirm-employee-no');
        const currName = document.getElementById('current-employee-name');
        const cfmName = document.getElementById('confirm-employee-name');
        const currDept = document.getElementById('current-employee-department');
        const cfmDept = document.getElementById('confirm-employee-department');
        const currUnit = document.getElementById('current-employee-unit');
        const cfmUnit = document.getElementById('confirm-employee-unit');
    
        const employeeTempStaff = document.getElementById('temp-staff');
        const employeeStaffOption = document.getElementById(`temp-staff-option-${employeeTempStaff.value}`);

        const staff = Object.values(allStaff[employeeDiscipline.value]).find(value => value['Employee_No'] === employeeNo.value);

        const tempCode = employeeTempStaff.value;

        
        if (staff && tempCode) {
            
            alertMsgModal(
                'Both staff and temp staff have the same Employee_No.\nPlease enter different Employee_No.',
                'Employee Number Conflict'
            );
            employeeNo.focus();
            return;
            
        } else if (staff || tempCode) {
            
            // console.log(`Found staff: ${staff.Name} (${staff.Employee_No})`);
            
            let tempStaffName, tempStaffDept, tempStaffUnit;
            if (tempCode) {
                tempStaffName = employeeStaffOption.dataset.tempStaffName;
                tempStaffDept = employeeStaffOption.dataset.tempStaffDepartment;
                tempStaffUnit = employeeStaffOption.dataset.tempStaffUnit;
            }

            modalHeader.classList.remove('text-bg-success')
            modalHeader.classList.add('text-bg-danger')
            
            modalTitle.innerText = 'Update Staff';
            
            modalBodyTitle.innerText = 'Update Existing Staff?';
            currNo.innerText = `(current: ${staff ? staff['Employee_No'] : tempCode})`;
            cfmNo.value = employeeNo.value;
            currName.innerText = `(current: ${staff ? staff.Name : tempStaffName})`;
            cfmName.value = employeeName.value ? employeeName.value : staff ? staff.Name : tempStaffName;
            employeeName.value = cfmName.value;
            currDept.innerText = `(current: ${staff ? staff.Department : tempStaffDept})`;
            cfmDept.value = employeeDepartment.value;
            currUnit.innerText = `(current: ${staff ? staff.Unit : tempStaffUnit})`;
            cfmUnit.value = employeeUnit.value;
            isUpdate.value = 'true';
            
        } else {

            modalHeader.classList.remove('text-bg-danger')
            modalHeader.classList.add('text-bg-success')
            
            modalTitle.innerText = 'Add Staff';
            modalBodyTitle.innerText = 'Add New Staff?';

            currNo.innerText = "";
            cfmNo.value = employeeNo.value;
            currName.innerText = "";
            cfmName.value = employeeName.value;
            currDept.innerText = "";
            cfmDept.value = employeeDepartment.value;
            currUnit.innerText = "";
            cfmUnit.value = employeeUnit.value;
            isUpdate.value = 'false';
        }

        const modal = new bootstrap.Modal(document.getElementById('confirm-add-staff-modal'));
        modal.show();
    })
}

function listenConfirmBtn() {
    const confirmBtn = document.getElementById('confirm-btn');
    confirmBtn.addEventListener('click', () => {
        console.log('Confirm button pressed')
        const addForm = document.getElementById('add-form');
        // check if form valid
        if (addForm.checkValidity()) {
            addForm.submit();
        } else {
            // wait for the modal to fully disappear
            setTimeout(() => {
                addForm.reportValidity();
            }, 500);
        }
    })
}

function listenFromTemp() {
    const fromTempCb = document.getElementById('from-temp');
    fromTempCb.addEventListener('change', () => {
        // check if fromTempCb is checked, if checked, show the select element of id 'temp-staff', and vice versa
        const tempStaff = document.getElementById('temp-staff');
        const tempStaffOptionDefault = document.getElementById('temp-staff-option-default');

        if (fromTempCb.checked) {
            tempStaff.style.display = 'block';
            fromTempCb.value = 'true';

        } else {
            tempStaff.style.display = 'none';
            fromTempCb.value = 'false';
            tempStaffOptionDefault.selected = true;
        }
    })
}

function listenDisciplineRadio() {

    const disciplineRadios = document.querySelectorAll('input[name="employee_discipline"]');
    disciplineRadios.forEach(discipline => {
        discipline.addEventListener('change', () => {updateDisciplineRadios(discipline)});
    })
    
    function updateDisciplineRadios(discipline) {
        const fromTempCb = document.getElementById('from-temp');
        const tempStaffOptionDefault = document.getElementById('temp-staff-option-default');

        // if radio with value nurse is checked, hide the select element of id 'temp-staff', and vice versa
        if (discipline.value === 'nurse' && discipline.checked) {
            document.querySelectorAll('.temp-staff-display').forEach(element => {
                element.style.display = 'none';
            });
            if (fromTempCb.checked) {
                fromTempCb.click()
            }
            tempStaffOptionDefault.selected = true;
        } else {
            document.querySelectorAll('.temp-staff-display').forEach(element => {
                element.style.removeProperty('display');
            });
        }
    }

    // dispatch change event of the first radio
    disciplineRadios[0].dispatchEvent(new Event('change'));

}

document.addEventListener('DOMContentLoaded', () => {
    initialize();
});