import { dynamicUnitDropdown } from "./utils.js";

// Function to read data attributes
function getDataAttributes() {
	const container = document.getElementById('data-container');
	if (!container) return {};
  
	return {
	  pcas: JSON.parse(container.dataset.pcas),
	  tempStaff: JSON.parse(container.dataset.tempStaff),
	  nurses: JSON.parse(container.dataset.nurses),
	  unitsByDepartment: JSON.parse(container.dataset.unitsByDepartment),
	  passingCriteria: JSON.parse(container.dataset.passingCriteria),
	  checklistInfo: JSON.parse(container.dataset.checklistInfo),
	  ranks: JSON.parse(container.dataset.ranks),
	  isPcaAssessmentForm: container.dataset.isPcaAssessmentForm
	};
}
  
  // Initialize the form
function initializeForm() {

	const { 
		pcas, 
		tempStaff, 
		nurses, 
		unitsByDepartment, 
		passingCriteria, 
		checklistInfo, 
		ranks, 
		isPcaAssessmentForm 
	} = getDataAttributes();

	// Call your functions with the data
	listenFormSubmit(pcas, tempStaff, nurses);
	setEntryInputs(ranks);
	resetForm();
	listenDropdowns('.select-unit', unitsByDepartment);

	if (checklistInfo.option_setting === 'one') {
		one_option_only();
	}

	if (isPcaAssessmentForm === "True") {
		pcaFormFillNames()
	}

	listenRadios(passingCriteria);
	updateTotalScore(passingCriteria);
	listenResetButtons(passingCriteria);
	listenCheckAll();
	listenResetAll();
}

function resetForm() {
	document.querySelectorAll('textarea').forEach(textarea => {
		textarea.value = ''
		const dates = document.querySelectorAll('input[type="date"]');
		dates.forEach(date => {
			date.value = new Date().toISOString().split('T')[0]
		})
	})
}
  
function toggleRadio(btn, passingCriteria) {
	const radios = btn.previousElementSibling.querySelectorAll('input');
	radios.forEach(radio => {
		radio.checked = false;
	})
	updateTotalScore(passingCriteria);
}

function listenResetButtons(passingCriteria) {
	document.querySelectorAll('button.reset-button').forEach(btn => {
		btn.addEventListener('click', () => {
			toggleRadio(btn, passingCriteria);
		})
	})
}

function checkAll(btn) {
	const col = btn.getAttribute('data-column');
	// Get the elements of class col and click them
	if (col) {
		document.querySelectorAll(`input.${col}`).forEach(element => {
			element.click();
		});
	}
}

function resetAll(btn) {
	const col = btn.getAttribute('data-column');
	// Get the elements of class col and click them
	if (col) {
		document.querySelectorAll(`button.${col}`).forEach(element => {
			element.click();
		});
	}
}

function setEntryInputs(ranks) {
	document.querySelectorAll('input.entry-input').forEach(input => {
		switch (input.getAttribute('data-input-type')) {
			case 'test_date':
				input.type = 'date';
				input.name = 'test_date_' + input.name;
				break;
			case 'date':
				input.type = 'date';
				break;
			case 'number':
				input.type = 'number';
				break;
			case 'checkbox':
				input.type = 'checkbox';
				break;
			case 'free_text':
				input.type = 'text';
				break;
			case 'rank':
				// create a select element next to the input
				const select = document.createElement('select');
				select.name = input.name;
				select.id = input.id;
				select.className = 'form-select entry-input';
				input.parentNode.insertBefore(select, input.nextSibling);
				if (input.hasAttribute('required')) {
					select.required = true;
				}

				ranks.forEach(rank => {
					const option = document.createElement('option');
					option.value = rank;
					option.textContent = rank;
					select.appendChild(option);
				})

				// Check the option "APN / NO" by default
				const defaultOption = select.querySelector('option[value="APN / NO"]');
				if (defaultOption) {
					defaultOption.selected = true;
				}

				// remove the input element
				input.remove();

				break;
			// default:
				// input.setAttribute('list', `listOptions-${input.id}`);
				// input.setAttribute('placeholder', 'Type here to search...');

				// const dataList = document.createElement('datalist');
				// dataList.id = `listOptions-${input.id}`;
				// input.required = true;
				
				// const options = JSON.parse(input.getAttribute('data-input-type'));
				// options.forEach(option => {
				// 	const opt = document.createElement('option');
				// 	opt.value = option;
				// 	opt.textContent = option;
				// 	dataList.appendChild(opt);
				// });

				// input.parentNode.appendChild(dataList);
		}
})
}


function createDepartmentSelect(input, departments) {

	const selectElement = document.createElement('select');
	selectElement.name = 'department';
	selectElement.id = 'department';
	selectElement.className = 'form-select';

	departments.forEach(dept => {
		const optionElement = document.createElement('option');
		optionElement.value = dept;
		optionElement.textContent = dept;
		selectElement.appendChild(optionElement);
	});

	const labelElement = document.createElement('label');
	labelElement.htmlFor = 'department';
	labelElement.textContent = 'Department:';

	const formGroupElement = document.createElement('div');
	formGroupElement.className = 'form-group mt-3';
	formGroupElement.appendChild(labelElement);
	formGroupElement.appendChild(selectElement);

	// append the formGroupElement to your desired parent element

}


function listenFormSubmit(pcas, temp_staff, nurses) {

	const form = document.querySelector('#complete-form')
	const submitBtn = form.querySelector('#form-submit')

	submitBtn.addEventListener('click', (e) => {

		console.log('Submit button pressed')
		
		e.preventDefault()

		if (!form.checkValidity()) {
			form.reportValidity();
			return;
		}
		
		const deptInput = form.querySelector('#department')
		const unitInput = form.querySelector('#unit')
		const nameInput = form.querySelector('#name')
		const employeenoInput = form.querySelector('#employeeno')
		
		const modalDept = document.querySelector('#modal-staff-dept')
		const modalUnit = document.querySelector('#modal-staff-unit')
		const modalName = document.querySelector('#modal-staff-name')
		const modalEmployeeNo = document.querySelector('#modal-staff-employeeno')
		const heading = document.querySelector('#found-staff')
		
		let foundOp = Object.entries(pcas).find(([key, _]) => key === employeenoInput.value);

		if (!foundOp) {
			foundOp = Object.entries(temp_staff).find(([key, _]) => key === employeenoInput.value);
		} else if (!foundOp) {
			foundOp = Object.entries(nurses).find(([key, _]) => key === employeenoInput.value);
		}

		if (foundOp) {

			console.log('Found Staff')

			const foundName = foundOp[1]['Name']
			const foundDept = foundOp[1]['Department']
			const foundUnit = foundOp[1]['Unit']

			console.log(`${foundName} ${foundDept} ${foundUnit}`)


			// Update the form inputs
			nameInput.value = foundName
			deptInput.value = foundDept
			deptInput.querySelector(`option[value="${foundDept}"]`).selected = true
			deptInput.dispatchEvent(new Event('change'))
			unitInput.value = foundUnit
			unitInput.querySelector(`option[value="${foundUnit}"]`).selected = true

			// Update the modal inputs
			modalDept.value = foundDept
			modalUnit.value = foundUnit
			modalName.value = foundName
			modalEmployeeNo.value = foundOp[0]


			heading.innerHTML = 'Update Existing Staff'
			heading.classList.add('text-success')

		} else {

			console.log('New Staff')

			// Check form validation
			if (!form.checkValidity()) {
				alert('Please fill in all required fields')
				return
			}


			// Update the modal inputs
			modalDept.value = deptInput.value
			modalUnit.value = unitInput.value
			modalName.value = nameInput.value
			modalEmployeeNo.value = employeenoInput.value

			heading.innerHTML = 'Create New Staff Profile'
			heading.classList.add('text-danger')

		}

		// Display the confirmation dialog
		const modal = new bootstrap.Modal(document.querySelector('#confirm-staff-modal'));
		modal.show();

	})

	const confirmBtn = document.querySelector('#confirm-staff-btn')
	confirmBtn.addEventListener('click', (e) => {

		// Check form validation
		if (!form.checkValidity()) {
			alert('Please fill in all required fields')
		} else {
			form.submit();
		}

	})
	
}


function one_option_only() {
	// Get the elements of class "checklist-options",
	// then add a click event listener to each element
	// if the elements have the same value of attribute "data-options",
	// when one is clicked, others will be unchecked

	const options = document.querySelectorAll('.checklist-options');
	options.forEach(option => {
		option.addEventListener('click', () => {
			const dataOptions = option.getAttribute('data-options');
			document.querySelectorAll(`.${dataOptions}`).forEach(allOption => {
				allOption.checked = false;
			});
			option.checked = true;
		});
	});
}


// Function to calculate and update the total score
function updateTotalScore(passingCriteria) {
  
	// Initialize total score and full score
	let totalScore = 0;
	let fullScore = 0;
  
	// Track if all essential questions are checked
	let allEssentialsChecked = true;
  
	// Loop through all <th> elements in the table header
	document.querySelectorAll('#checklist-content thead th').forEach((th, index) => {
	  // Find the <div> inside the <th> and check if it has data-cs="true"
	  const div = th.querySelector('div');
	  if (div && div.getAttribute('data-cs') === 'true') {

		// Get the column index
		const columnIndex = index;
  
		// Count the number of checked radio buttons in this column
		const checkedCount = document.querySelectorAll(
		  `#checklist-content tbody td:nth-child(${columnIndex + 1}) input[type="radio"]:checked`
		).length;
  
		// Add to the total score
		totalScore += checkedCount;
  
		// Calculate the full score (total number of questions in this column)
		const fullCount = document.querySelectorAll(
		  `#checklist-content tbody td:nth-child(${columnIndex + 1}) input[type="radio"]`
		).length;
		fullScore += fullCount;
  
		// Check essential questions if required
		if (passingCriteria.essential) {
		  document.querySelectorAll(
			`#checklist-content tbody td:nth-child(${columnIndex + 1}) input[type="radio"]`
		  ).forEach((radio) => {
			// Find the row of the radio button,
			// then check if the first <td> has data-essential="true"
			// If so, check if the radio button is checked
			// If not, set allEssentialsChecked to false
			const row = radio.closest('tr');
			const essentialCell = row.querySelector('td[data-essential="true"]');
			if (essentialCell && !radio.checked) {
			  allEssentialsChecked = false;
			}
		  });
		}
	  }
	});
  
	// Update the "TOTAL SCORE" display
	const totalScoreElement = document.getElementById('total-score');
	const totalScoreInput = document.getElementById('total-score-input');
	if (totalScoreElement) {
	  totalScoreElement.textContent = `${totalScore} / ${fullScore}`;
	  totalScoreInput.value = totalScore;
	}
  
	// Calculate the percentage score
	const percentageScore = fullScore === 0 ? 0 : (totalScore / fullScore) * 100;
  
	// Determine PASS/FAIL
	const passFailElement = document.getElementById('pass-fail');
	const passFailInput = document.getElementById('pass-fail-input');
	if (passFailElement) {
	  if (passingCriteria.essential && !allEssentialsChecked) {
		// Fail if essential questions are not all checked
		passFailElement.textContent = 'FAIL';
		passFailElement.style.color = 'red';
		passFailInput.value = 'fail';
	  } else if (percentageScore >= passingCriteria.passing_pct) {
		// Pass if percentage score meets the criteria
		passFailElement.textContent = 'PASS';
		passFailElement.style.color = 'green';
		passFailInput.value = 'pass';
	  } else {
		// Fail otherwise
		passFailElement.textContent = 'FAIL';
		passFailElement.style.color = 'red';
		passFailInput.value = 'fail';
	  }
	}
  }
  


function listenRadios(passingCriteria) {
// Attach event listeners to all radio buttons
document.querySelectorAll('#checklist-content tbody input[type="radio"]').forEach((radio) => {
	radio.addEventListener('change', () => {
	updateTotalScore(passingCriteria);
	});
});
}
  

function listenDropdowns(elementSelection, dropdownList) {

	document.querySelectorAll(elementSelection).forEach(select => {

		const unitID = select.id
		const deptID = unitID.replace('_u','_d')
	  
		dynamicUnitDropdown(deptID,unitID,dropdownList)
	  
	})

	dynamicUnitDropdown('department','unit', dropdownList)

}

function listenCheckAll() {
	const checkAllButtons = document.querySelectorAll('button.check-all');
	checkAllButtons.forEach(btn => {
		btn.addEventListener('click', () => {
			checkAll(btn);
		});
	});
}

function listenResetAll() {
	const resetAllButtons = document.querySelectorAll('button.reset-all');
	resetAllButtons.forEach(btn => {
		btn.addEventListener('click', () => {
			resetAll(btn);
		});
	});
}


function pcaFormFillNames() {

	// Find the card container by the 指導員姓名 title
	const instructorTitle = document.evaluate(
		"//span[text()='指導員姓名']",
		document,
		null,
		XPathResult.FIRST_ORDERED_NODE_TYPE,
		null
	).singleNodeValue;

	if (!instructorTitle) return;

	// Find the form group containing 指導員姓名
	const instructorGroup = document.evaluate(
		"//span[text()='指導員姓名']/ancestor::div[contains(@class, 'form-group')][1]",
		document,
		null,
		XPathResult.FIRST_ORDERED_NODE_TYPE,
		null
	).singleNodeValue;

	if (!instructorGroup) return;

	// Create button container with Bootstrap flex layout
	const buttonContainer = document.createElement('div');
	buttonContainer.className = 'd-flex justify-content-end gap-2 mt-2';

	// Create buttons
	const naButton = document.createElement('button');
	naButton.className = 'btn btn-sm btn-outline-secondary';
	naButton.type = 'button';
	naButton.textContent = 'N/A';

	const applyButton = document.createElement('button');
	applyButton.className = 'btn btn-sm btn-outline-primary';
	applyButton.type = 'button';
	applyButton.textContent = 'Apply to ALL';

	// Insert buttons after the input group
	buttonContainer.appendChild(naButton);
	buttonContainer.appendChild(applyButton);
	
	// Add buttons to the form group
	instructorGroup.parentNode.insertBefore(buttonContainer, instructorGroup);


	// Function to find input by associated label text
	const getInputByLabelText = (text) => {
		const span = document.evaluate(
			`//span[text()='${text}']`,
			document,
			null,
			XPathResult.FIRST_ORDERED_NODE_TYPE,
			null
		).singleNodeValue;
		
		if (!span) {
			console.log(`Label not found for ${text}`);
			return null;
		}
		// if text includes :, it's a select.
		// find the select next to the span down the DOM tree
		if (text.includes(':')) {
			return span.nextElementSibling
		} else {
			return span.closest('.form-group')?.querySelector('input');
		}
	};

	// N/A Button handler
	naButton.addEventListener('click', () => {
		const fieldsToToggle = [
			'指導員姓名',
			'指導員職級:', 
			'指導講解日期',
			'觀察員姓名',
			'觀察員職級:', 
			'觀察下實習日期'
		];

		fieldsToToggle.forEach((text) => {
			const element = getInputByLabelText(text);
			if (element) element.disabled = !element.disabled;
		});
	});

	// Apply to ALL Button handler
	applyButton.addEventListener('click', () => {

		// Source elements
		const sourceName = getInputByLabelText('指導員姓名');
		const sourceRank = getInputByLabelText('指導員職級:'); // First 職級 (指導員)
		const sourceDate = getInputByLabelText('指導講解日期');

		console.log(sourceName.name)
		console.log(sourceRank.name)
		console.log(sourceDate.name)

		// Copy to 觀察員 section
		getInputByLabelText('觀察員姓名').value = sourceName.value;
		getInputByLabelText('觀察員職級:').value = sourceRank.value;
		getInputByLabelText('觀察下實習日期').value = sourceDate.value;

		// Copy to 評核員 section
		getInputByLabelText('評核員姓名').value = sourceName.value;
		getInputByLabelText('職級:').value = sourceRank.value;

		getInputByLabelText('日期').value = sourceDate.value;
	});
}


// Call the initialize function when the DOM is loaded
document.addEventListener('DOMContentLoaded', initializeForm);


