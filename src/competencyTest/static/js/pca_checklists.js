import { dynamicUnitDropdown } from "./utils.js";

function getDataAttributes() {
	const container = document.getElementById('data-container');
	if (!container) return {};
  
	return {
	  pcas: JSON.parse(container.dataset.pcas),
	  tempStaff: JSON.parse(container.dataset.tempStaff),
	  unitsByDepartment: JSON.parse(container.dataset.unitsByDepartment),
	  nextTempIdx: container.dataset.nextTempIdx
	};
}

function initialize(){
	const { pcas, tempStaff, unitsByDepartment, nextTempIdx } = getDataAttributes();

	dynamicUnitDropdown('pca_dept','pca_unit',unitsByDepartment)
	listenFormSubmit(pcas, tempStaff)
	listenOldTempStaff()
	listenNewTempStaff(nextTempIdx)
	listenApplyToAll()
	listenSelectAllBtn()
	listenUnselectAllBtn()
	initializeDates()
}


function listenOldTempStaff() {
	document.getElementById('old_temp_staff').addEventListener('click', (e) => {
		toggleOldTemp(e.target)
	})
}

function listenNewTempStaff(nextTempIdx) {
	document.getElementById('new_temp_staff').addEventListener('click', (e) => {
		toggleTempStaff(e.target, nextTempIdx)
	})
}

function listenSelectAllBtn() {
	document.getElementById('selectAllBtn').addEventListener('click', () => {
		selectAll()
	})
}

function listenUnselectAllBtn() {
	document.getElementById('unselectAllBtn').addEventListener('click', () => {
		unselectAll()
	})
}

function initializeDates() {
	const dates = document.querySelectorAll('input.org-dates');
	dates.forEach(date => {
		date.value = new Date().toISOString().split('T')[0]
	})
}

function listenApplyToAll() {
	const applyToAll = document.getElementById('apply-to-all')
	applyToAll.addEventListener('click', () => {
		const organizerName = document.getElementById('tutor_name').value
		const organizerRank = document.getElementById('tutor_rank').value
		const organizerDate = document.getElementById('tutor-date').value	

		document.querySelectorAll('.org-names'). forEach(orgName => {
			orgName.value = organizerName
		})

		document.querySelectorAll('.org-ranks'). forEach(orgRank => {
			orgRank.value = organizerRank
			orgRank.querySelector(`option[value="${organizerRank}"]`).selected = true
		})

		document.querySelectorAll('.org-dates'). forEach(orgDate => {
			orgDate.value = organizerDate
		})
	})
}

function selectAll() {
	const testOptions = document.querySelectorAll('.test-option');
	testOptions.forEach(testOption => {
		testOption.checked = true
	})
}

function unselectAll() {
	const testOptions = document.querySelectorAll('.test-option');
	testOptions.forEach(testOption => {
		testOption.checked = false
	})
}


function listenFormSubmit(pcas, temp_staff) {
	const form = document.querySelector('#complete-form')
	const submitBtn = form.querySelector('#form-submit')

	submitBtn.addEventListener('click', (e) => {

		console.log('Submit button pressed')
		
		e.preventDefault()

		
		const pcaDept = form.querySelector('#pca_dept')
		const pcaUnit = form.querySelector('#pca_unit')
		const pcaName = form.querySelector('#pca_name')
		const pcaEmployeeNo = form.querySelector('#pca_employeeno')
		
		const modalDept = document.querySelector('#modal-pca-dept')
		const modalUnit = document.querySelector('#modal-pca-unit')
		const modalName = document.querySelector('#modal-pca-name')
		const modalEmployeeNo = document.querySelector('#modal-pca-employeeno')
		const heading = document.querySelector('#found-pca')
		
		let foundOp = Object.entries(pcas).find(([key, value]) => key === pcaEmployeeNo.value);
		
		if (!foundOp) {
			foundOp = Object.entries(temp_staff).find(([key, value]) => key === pcaEmployeeNo.value);
		}
		
		if (foundOp) {
			
			console.log('Found PCA')
			
			const foundName = foundOp[1]['Name']
			const foundDept = foundOp[1]['Department']
			const foundUnit = foundOp[1]['Unit']
			
			console.log(`${foundName} ${foundDept} ${foundUnit}`)
			
			pcaName.value = foundName
			pcaDept.value = foundDept
			pcaDept.querySelector(`option[value="${foundDept}"]`).selected = true
			pcaDept.dispatchEvent(new Event('change'))
			pcaUnit.value = foundUnit
			pcaUnit.querySelector(`option[value="${foundUnit}"]`).selected = true
			
			modalDept.value = foundDept
			modalUnit.value = foundUnit
			modalName.value = foundName
			modalEmployeeNo.value = foundOp[0]
			
			
			heading.innerHTML = 'Update Existing PCA'
			heading.classList.add('text-success')
			
		} else {
			
			console.log('New PCA')
			
			modalDept.value = pcaDept.value
			modalUnit.value = pcaUnit.value
			modalName.value = pcaName.value
			modalEmployeeNo.value = pcaEmployeeNo.value
			
			heading.innerHTML = 'Create New PCA Profile'
			heading.classList.add('text-danger')
			
		}
		
		if (!form.checkValidity()) {
			form.reportValidity()
			return
		}
		
		// Display the confirmation dialog
		const modal = new bootstrap.Modal(document.querySelector('#confirm-pca-modal'));
		modal.show();
		
	})
	
	const confirmBtn = document.querySelector('#confirm-pca-btn')
	confirmBtn.addEventListener('click', (e) => {

		// Check form validation
		if (!form.checkValidity()) {
			alert('Please fill in all required fields')
		} else {
			form.submit();
		}

	})
	
}

function toggleTempStaff(checkbox, next_temp_idx) {
	const pcaEmployeeno = document.querySelector('#pca_employeeno')
	const tempStaffNo = document.querySelector('#new_temp_staff_no')
	const oldTempBtn = document.querySelector('#old_temp_staff')
	if (checkbox.checked) {
		// disable the element pcaEmployeeno
		pcaEmployeeno.disabled = true
		pcaEmployeeno.setAttribute('type', 'text')
		pcaEmployeeno.value = 'TEMP' + next_temp_idx
		tempStaffNo.value = 'TEMP' + next_temp_idx
		oldTempBtn.disabled = true
		oldTempBtn.checked = false
	} else {
		pcaEmployeeno.disabled = false
		pcaEmployeeno.setAttribute('type', 'number')
		pcaEmployeeno.value = ''
		oldTempBtn.disabled = false
	}

}

function toggleOldTemp(checkbox) {
	const new_temp_checkbox = document.querySelector('#new_temp_staff')
	const pcaEmployeeno = document.querySelector('#pca_employeeno')
	if (checkbox.checked) {
		new_temp_checkbox.checked = false
		new_temp_checkbox.disabled = true
		pcaEmployeeno.setAttribute('type', 'text')
	} else {
		new_temp_checkbox.disabled = false
		pcaEmployeeno.setAttribute('type', 'number')
	}
}


document.addEventListener('DOMContentLoaded', () => {
	initialize()
})