function getDataAttributes() {
  const container = document.getElementById('data-container');
  if (!container) return {};

  return {
    queryBy: container.dataset.queryBy,
    queryTest: container.dataset.queryTest,
    resultData: JSON.parse(container.dataset.resultData),
    testForm: JSON.parse(container.dataset.testForm),
    urlForView: container.dataset.urlForView
  };
}

// Function to initialize the logic
function initialize() {
  const { queryBy, queryTest, resultData, testForm, urlForView } = getDataAttributes();

  // Call switchTabs
  switchTabs();

  // Call listenBackBtn
  listenBackBtn(urlForView);

  // Call listenPlainTableTestBtns
  listenPlainTableTestBtns();

  // Call person_default_display if query_by is 'person'
  if (queryBy === 'person') {
    person_default_display();
  }

  // Render the grid based on query_test and test_form
  if (queryTest !== 'ALL') {

    if (testForm.info.form_type === 'mc') {

      mc__renderAGgrid(resultData, testForm);

    } else if (testForm.info.form_type === 'checklist') {
      
      console.log('Rendering Table for Checklist')
      checklist__renderAGgrid(resultData, testForm);

    }
  } else {

    all__renderAGgrid(resultData);

  }

  modalOnHidden('mc-result');
  modalOnHidden('checklist-result');


}

function modalOnHidden(modalID) {
  const modal = document.getElementById(modalID)

  if (!modal) {
    console.log('Modal Not Found: ' + modalID);
    return;
  }

  // Find the button in the div .modal-header and add event listener to that button to blur on click
  const headerButtons = modal.querySelectorAll('.modal-header button');
  headerButtons.forEach(button => {
    button.addEventListener('click', () => {
      button.blur();
    });
  });

  // Find the button in the div .modal-footer and add event listener to that button to blur on click
  const footerButtons = modal.querySelectorAll('.modal-footer button');
  footerButtons.forEach(button => {
    button.addEventListener('click', () => {
      button.blur();
    });
  });
}

function listenBackBtn(urlForView) {

  const backBtn = document.getElementById('back-button');

  backBtn.addEventListener('click', () => {
    window.location.href = urlForView;
  });
}

function listenPlainTableTestBtns() {
  const plainTableTestBtns = document.querySelectorAll('.plain-table-test-btn');
  plainTableTestBtns.forEach(btn => {
    btn.addEventListener('click', (e) => {
      plainTableTestBtn__onClickHandler(e.currentTarget);
      // use currentTarget to get the clicked button but not other child elements
    });
  });
}

function all__renderAGgrid(rowData) {

  const rowDataExpanded = expandRows(rowData)

  const columnDefs = [
    // {field: "Unit", minWidth: 100},
    // {field: "Name"},
    // {field: "Employee_No", minWidth: 100},
    {field: "Test", minWidth: 100},
    {
      field: "Result",
      cellRenderer: params => {
        return renderResult(params)
      },
      valueFormatter: params => {
        // Return a string representation of the object for AG Grid's internal use
        if (params.value === "[ NIL ]") {
          return "[ NIL ]";
        } else if (params.value && typeof params.value === 'object') {
          return params.value.pass ? 'PASS' : 'FAIL';
        }
        return params.value;
      },
      comparator: (valueA,valueB) => {
        return sortByPassFail(valueA,valueB)
      }
    }

  ];

  const defaultColDef = {
    flex: 1,
    minWidth: 150,
    sortable: true,
    filter: true,
  };

  const gridOptions = {
    columnDefs: columnDefs,
    defaultColDef: defaultColDef,
    rowData: rowDataExpanded,
    pagination: true,
    paginationPageSize: 50,
  };

  document.addEventListener('DOMContentLoaded', () => {
    const gridDiv = document.querySelector('#grid');

    // Clear any existing grid content
    gridDiv.innerHTML = '';

    agGrid.createGrid(gridDiv, gridOptions);
  });
};


function expandRows(rowData) {

  const rowDataExpanded = []

  // console.log(`Length of rowData: ${rowData.length}`)

  for (const row of rowData) {
    // console.log(`Length of result: ${row['Result'].length}`)
    // check if it is a list
    if (row['Result'] == '[ NIL ]') {
      rowDataExpanded.push({
        Test: '[ NIL ]',
        Result: '[ NIL ]'
      })
    } else {
      for (const result of row['Result']) {
        rowDataExpanded.push({
          Test: result['test_name'],
          Result: result
        })
      }
    }
  }

  return rowDataExpanded

}

function mc__renderAGgrid(rowData, testForm) {

  const columnDefs = [
    {field: "Unit", minWidth: 100},
    {field: "Name"},
    {field: "Employee_No", minWidth: 100},
    {
      field: "Result",
      cellRenderer: params => {
        return renderResult(params)
      },
      valueFormatter: params => {
        // Return a string representation of the object for AG Grid's internal use
        if (params.value === "[ NIL ]") {
          return "[ NIL ]";
        } else if (params.value && typeof params.value === 'object') {
          return params.value.pass ? 'PASS' : 'FAIL';
        }
        return params.value;
      },
      comparator: (valueA,valueB) => {
        return sortByPassFail(valueA,valueB)
      }
    }

  ];

  const defaultColDef = {
    flex: 1,
    minWidth: 150,
    sortable: true,
    filter: true,
  };

  const gridOptions = {
    columnDefs: columnDefs,
    defaultColDef: defaultColDef,
    rowData: rowData,
    pagination: true,
    paginationPageSize: 50,
    onCellClicked: params => {
      mcResult__onClick(params, testForm)
    }
      
  };

  const gridDiv = document.querySelector('#grid');

  // Clear any existing grid content
  gridDiv.innerHTML = '';

  agGrid.createGrid(gridDiv, gridOptions);


};

function checklist__renderAGgrid(rowData, testForm) {

  console.log(rowData)

  const columnDefs = [
    {field: "Unit", minWidth: 100},
    {field: "Name"},
    {field: "Employee_No", minWidth: 100, headerName: 'Employee No.'},
    {
      field: "Result",
      cellRenderer: params => {
        return renderResult(params)
      },
      valueFormatter: params => {
        // Return a string representation of the object for AG Grid's internal use
        if (params.value === "[ NIL ]") {
          return "[ NIL ]";
        } else if (params.value && typeof params.value === 'object') {
          return params.value.pass ? 'PASS' : 'FAIL';
        }
        return params.value;
      },
      comparator: (valueA,valueB) => {
        return sortByPassFail(valueA,valueB)
      }
    }

  ];

  const defaultColDef = {
    flex: 1,
    minWidth: 150,
    sortable: true,
    filter: true,
  };

  const gridOptions = {
    columnDefs: columnDefs,
    defaultColDef: defaultColDef,
    rowData: rowData,
    pagination: true,
    paginationPageSize: 50,
    onCellClicked: params => {
      checklistResult__onClick(params, testForm)
    }

  };


  const gridDiv = document.querySelector('#grid');

  // Clear any existing grid content
  gridDiv.innerHTML = '';

  agGrid.createGrid(gridDiv, gridOptions);



};



function sortByPassFail(valueA, valueB) {
  if (valueA.pass && !valueB.pass) {
    return 1;
  } else if (!valueA.pass && valueB.pass) {
    return -1;
  } else {
    return 0;
  }
};

function renderResult(params) {
  if(params.value == "[ NIL ]") {
    return '[ NIL ]';
  } else {
    console.log('Rendering Pass Fail Button')
    const btnText = params.data.Result.pass
    ? `<button id="ag-${params.data.Employee_No}" style="background: none; border: none; color: green;">
      <strong>Pass on ${params.data.Result.time.split(' ')[0]}</strong>
    </button>`
    : `<button id="ag-${params.data.Employee_No}" style="background: none; border: none; color: red;">
      <strong>FAIL on ${params.data.Result.time.split(' ')[0]}</strong>
    </button>`;
    return btnText;
  }
};


function mcResult__onClick(params, testForm) {

  if (params.column.getColId() === 'Result' && params.value != '[ NIL ]') {

    var modalBody = document.getElementById('mc-content');

    const formContentObj = params.data.Result.result;

    console.log(formContentObj)

    // Add replies to the form
    for (const [key, value] of Object.entries(formContentObj)) {
      const div = document.getElementById(`div-${key}`);
      if (value.ans == value.reply){
          div.innerHTML = `
            <input 
              type="radio" 
              id="${key}" 
              value="${value.reply}" 
              checked 
              disabled
            >
            <label 
              style="color: green;" 
              for="${key}"
            ><strong>${value.reply}</strong></label>`;
        } else {
          div.innerHTML = `
          <input 
            type="radio" 
            id="${key}" 
            value="${value.reply}" 
            checked 
            disabled
          >
          <label 
            style="color: red;" 
            for="${key}"><strong>${value.reply}<strong></label>
          <div>
            <input type="radio" id="${key}-ans" value="${value.ans}" checked disabled>
            <label style="color: #c7edec; " for="${key}-ans">${value.ans}</label>
          </div>`;
        }

    }

    document.querySelector("#mc-result-title").innerHTML = `${params.data.Name} (${params.data.Employee_No}) [${params.data.Result.score}]`;

    $('#mc-result').modal('show');
  } else if (params.column.getColId() === 'Result' && params.value == '[ NIL ]' && params.data.Employee_No != '[ NIL ]') {

    document.querySelector('#fill-in-assessment').innerHTML = `Fill in Assessment Form for <b>${params.data.Name} (${params.data.Employee_No})</b>?`;

    // Show the confirmation modal
    $('#confirm-redirect').modal('show');

    // Fill hidden form fields
    document.getElementById('redirect-employeeNo').value = params.data.Employee_No;

    // Fill in which form to redirect
    document.getElementById('redirect-test').value = testForm.info.test_name;

    // Add event to the confirm button
    document.querySelector("#confirm-rdr-btn").onclick = function () {

      console.log('Confirm redirect');

      // Submit the form
      document.querySelector("#redirect-form").submit();

    };


  }
};

function checklistResult__onClick(params, testForm) {

  if (params.column.getColId() === 'Result' && params.value != '[ NIL ]') {

    updateChecklistModal(testForm)

    const modalBody = document.getElementById('checklist-content');

    const formContentObj = params.data.Result.result;

    fillChecklist(modalBody, formContentObj);

    document.querySelector("#checklist-result-title").innerHTML = `${params.data.Name} (${params.data.Employee_No}) [${params.data.Result.pass ? 'PASS' : 'FAIL'}]`;

    $('#checklist-result').modal('show');

  }

  if (params.column.getColId() === 'Result' && params.value == '[ NIL ]' && params.data.Employee_No != '[ NIL ]') {

    document.querySelector('#fill-in-assessment').innerHTML = `Fill in Assessment Form for <b>${params.data.Name} (${params.data.Employee_No}) </b>?`;

    // Show the confirmation modal
    $('#confirm-redirect').modal('show');

    // Fill hidden form fields
    document.getElementById('redirect-employeeNo').value = params.data.Employee_No;

    // Fill in which form to redirect
    document.getElementById('redirect-test').value = testForm.info.test_name;


    // Add event to the confirm button
    document.querySelector("#confirm-rdr-btn").onclick = function () {

      console.log('Confirm redirect');

      // Submit the form
      document.querySelector("#redirect-form").submit();

    };


  }
};



function switchTabs() {

  const tabs = document.querySelectorAll('.nav-link');

  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');

      switch (tab.getAttribute('id')) {
        case 'tab-ag':
        var activeTable = document.getElementById('result-ag');
        break;
        case 'tab-tr':
        var activeTable = document.getElementById('result-tr');
        break;
      }

      const tables = document.querySelectorAll('.tab-pane');
      tables.forEach(t => t.classList.remove('show', 'active'));
      activeTable.classList.add('show','active');

    });

  });
}

function person_default_display() {
  const tab = document.getElementById('tab-tr');
  tab.dispatchEvent(new Event('click'));

}

function exportTableToExcel() {
  gridOptions.api.exportDataAsCsv();
}


function plainTableTestBtn__onClickHandler(button) {

  const id = button.dataset.employeeNo;
  const testForm = JSON.parse(button.dataset.testForm);
  const name = button.dataset.name;
  const staffTest = JSON.parse(button.dataset.staffTest);


  // console.log(`testForm: ${testForm}`)  
  // console.log(`staffTest: ${staffTest}`)

  if (staffTest == "[ NIL ]" && id != '[ NIL ]') {

    document.querySelector('#fill-in-assessment').innerHTML = `Fill in Assessment Form for <b>${name}</b>?`;

    // Show the confirmation modal
    $('#confirm-redirect').modal('show');

    // Fill hidden form fields
    document.getElementById('redirect-employeeNo').value = id;

    // Fill in which form to redirect
    document.getElementById('redirect-test').value = testForm.info.test_name;


    // Add event to the confirm button
    document.querySelector("#confirm-rdr-btn").onclick = function () {
        console.log('Confirm redirect');

        // Submit the form
        document.querySelector("#redirect-form").submit();
    };
  } else if (staffTest != "[ NIL ]" && id != '[ NIL ]') {
    if (testForm.info.form_type == 'mc') {
      mcPlainShowForm(testForm,id,name,staffTest);
    } else if (testForm.info.form_type == 'checklist') {
      checklistPlainShowForm(name,staffTest,testForm);
    }
  }
}

function checklistPlainShowForm(name, staffTest, testForm) {

  updateChecklistModal(testForm)

  const modalBody = document.getElementById('checklist-content');

  const formContentObj = staffTest.result;

  fillChecklist(modalBody, formContentObj);

  document.querySelector("#checklist-result-title").innerHTML = `${name} - ${staffTest.test_name.replace('_', ' ')} [${staffTest.pass ? 'PASS' : 'FAIL'}]`;

  $('#checklist-result').modal('show');

}

function mcPlainShowForm(testForm,id,name,test) {

  updateMCModal(testForm)

  const formContentObj = test.result;

  // Add replies to the form
  for (const [key, value] of Object.entries(formContentObj)) {
    const div = document.getElementById(`div-${key}`);
    if (value.ans == value.reply) {
      div.innerHTML = `
        <input 
          type="radio" 
          id="${key}" 
          value="${value.reply}" 
          checked 
          disabled
        >
        <label 
          style="color: green;" 
          for="${key}"
        ><strong>${value.reply}</strong></label>`;

    } else {
      
      div.innerHTML = `
        <input type="radio" 
          id="${key}" 
          value="${value.reply}" 
          checked 
          disabled
        >
        <label 
          style="color: red;" 
          for="${key}"
        ><strong>${value.reply}<strong></label>
        <div>
          <input 
            type="radio" 
            id="${key}-ans" 
            value="${value.ans}" 
            checked 
            disabled
          >
            <label 
              style="color: #c7edec;" 
              for="${key}-ans"
            >${value.ans}</label>
        </div>`;
      }
    }

    document.querySelector("#mc-result-title").innerHTML = `${name} (${id}) [${test.score}]`;

    $('#mc-result').modal('show');
}

function updateMCModal(data) {

    // Set the modal title
    document.getElementById('mc-result-title').textContent = data.info['test_name'].replace(/_/g, ' ');

    // Initialize content variable
    let modalContent = '<form id="form-reply">';
  
    // Loop through the parts in the form data
    for (const [part, page] of Object.entries(rearrangeFormKeys(data.content))) {
  
      modalContent += `
        <div class="card mt-5">
          <div class="card-header">
            <h3>${page.header}</h3>
          </div>
          <div class="card-body">
            <div class="form-group">`
  
      if (part === 'intro') {
        modalContent += `
          <label 
            class="mt-3 mb-3"
          >${page.details}</label>`;
      } else if (part === 'questions') {

        for (const [id, item] of Object.entries(page.details)) {
  
          modalContent += `
                <label
                        class="question-text mt-3 mb-3"
                        for="${id}"
                      ><strong>${parseInt(id.replace('q', ''))} ) <br/>${item.q}</strong></label>`
                      
          if (item.opt.length > 0) {
            modalContent += `
              <div class="form-check" id="div-${id}"></div>`
          } else {
            modalContent += `
              <input
                style="display: none"
                type="text"
                id="div-${id}"
                class="form-control"
                disabled
              />`
          }
          
          // if this iteration is not the last one, add the horizontal line
          if (id !== Object.keys(page.details)[Object.keys(page.details).length - 1]) {
            modalContent += `
              <hr />`
          }

        }
                      
      }

      modalContent += `
            </div>
          </div>
        </div>`
    }

    // Close the form tag
    modalContent += `</form>`

    // Inject the generated content into the modal body
    document.getElementById('mc-content').innerHTML = modalContent;

}

function updateChecklistModal(data) {


  // Set the modal title
  document.getElementById('checklist-result-title').textContent = data.info['test_name'].replace(/_/g, ' ');

  // Initialize content variable
  let modalContent = '<form id="form-reply">';

  // Loop through the parts in the form data
  for (const [part, page] of Object.entries(rearrangeFormKeys(data.content))) {

    modalContent += `
      <div class="card mt-5">
        <div class="card-header">
          <h3>${page.header}</h3>
        </div>
        <div class="card-body">
          <div class="form-group">`

    if (part === 'intro') {
      modalContent += `<label class="mt-3 mb-3">${page.details}</label>`;
    } else if (part === 'questions') {

      modalContent += `
        <div class="table-responsive">
          <table class="table table-striped table-bordered table-hover" id="checklist-table">
            <thead>
              <tr>
                <th></th>
                <th class="h-center v-center bold-large">${page.itemcol}</th>`;

      for (const [k, opt] of Object.entries(page.details.options)) {
        modalContent += `<th class="h-center v-center bold-large">${opt.name}</th>`;
      }

      modalContent += `</tr></thead><tbody>`;

      for (const [id, item] of Object.entries(page.details.items)) {
        modalContent += `<tr>`;

        // Helper function to format question ID
        const formatQuestionId = (id) => {
          return id.replace(/^q0?/, '').replace(/_/g, '.');
        };

        // Handle different subtitle types
        if (item.subtitle === 1) {
          // Subtitle type 1 - main section header
          if (item.essential) {
            modalContent += `
              <td style="color: red; font-weight: bold" data-essential="true">
                <p>${formatQuestionId(id)}*</p>
              </td>`;
          } else {
            modalContent += `
              <td>
                <p>${formatQuestionId(id)}</p>
              </td>`;
          }
          modalContent += `
            <td colspan="${Object.keys(page.details.options).length + 1}">
              <p>${item.text}</p>
            </td>`;

        } else if (item.subtitle === 2) {
          // Subtitle type 2 - subsection header (italic)
          if (item.essential) {
            modalContent += `
              <td style="color: red; font-weight: bold;" data-essential="true">
                <p><i>${formatQuestionId(id)}*</i></p>
              </td>`;
          } else {
            modalContent += `
              <td>
                <p><i>${formatQuestionId(id)}</i></p>
              </td>`;
          }
          modalContent += `
            <td colspan="${Object.keys(page.details.options).length + 1}">
              <i>${item.text}</i>
            </td>`;

        } else {
          // Regular questions (subtitle 0, 3, or undefined)
          if (item.subtitle === 3) {
            // Subtitle type 3 - special layout with container-fluid
            modalContent += `
              <td></td>
              <td class="p-0">
                <div class="container-fluid p-0">
                  <div class="row g-0">
                    <div class="col-3 border-end py-2 px-1">`;
            if (item.essential) {
              modalContent += `<span style="color: red; font-weight: bold" data-essential="true">${formatQuestionId(id)}*</span>`;
            } else {
              modalContent += `<span>${formatQuestionId(id)}</span>`;
            }
            modalContent += `
                    </div>
                    <div class="col-9 py-2 px-1 item-content" data-essential="false">${item.text}</div>
                  </div>
                </div>
              </td>`;
          } else {
            // Regular layout
            if (item.essential) {
              modalContent += `<td style="color: red; font-weight: bold" data-essential="true">${formatQuestionId(id)}*</td>`;
            } else {
              modalContent += `<td>${formatQuestionId(id)}</td>`;
            }
            modalContent += `<td class="item-content" data-essential="false">${item.text}</td>`;
          }

          // Add option columns for regular questions
          for (const [k, opt] of Object.entries(page.details.options)) {
            if (opt.type === 'checkbox') {
              modalContent += `
                <td>
                  <div>
                    <input
                      type="radio"
                      class="btn-check checklist-options checkbox-cells ${id}_options col-${k}"
                      data-options="${id}_options"
                      id="${id}_${k}_tick"
                      value="true"
                      autocomplete="off"
                      name="${id}__${k}"
                    />
                    <label
                      class="btn btn-outline-success"
                      for="${id}_${k}_tick"
                    ><i class="fa-solid fa-check"></i></label>
                  </div>
                </td>`;
            } else {
              modalContent += `
                <td class="h-center">
                  <textarea
                    class="form-control textarea-cells"
                    name="${id}__${k}"
                    id="${id}_${k}"
                  ></textarea>
                </td>`;
            }
          }
        }
        modalContent += `</tr>`;
      }

      // Add total score section if any option has count_score = true
      const hasCountScore = Object.values(page.details.options).some(opt => opt.count_score === true);
      if (hasCountScore) {
        modalContent += `
          <tr>
            <td></td>
            <td>
              <span class="bold-large">TOTAL SCORE: </span>
              <span class="bold-large" id="total-score">--</span>
              <input type="hidden" name="total-score" id="total-score-input" value="0">
            </td>
            <td>
              <span class="bold-large" id="pass-fail">--</span>
            </td>
          </tr>`;
      }

      modalContent += `</tbody></table></div>`;

    } else {
    // Other parts

      for (const [k, item] of Object.entries(page.details)) {
        if (item.input === 'department_and_unit') {
          modalContent += `
            <div class="form-group input-group mt-3 mb-3">
              <span class="input-group-text">${item.title}</span>
              <div class="form-group mx-2">
                <textarea class="form-control textarea-cells" name="${k}_d" id="${k}_d"></textarea>
              </div>
              <div class="form-group">
                <textarea class="form-control textarea-cells" id="${k}_u"></textarea>
              </div>
            </div>`;
        } else {
          modalContent += `
            <div class="form-group input-group mt-3 mb-3">
              <span class="input-group-text">${item.title}</span>
              <textarea class="form-control textarea-cells" id="${k}" data-input-type="${item.input}"></textarea>
            </div>`;
        }
      }
    }

    modalContent += `
                </div>
            </div>
        </div>`;

  }

  // Close the form tag
  modalContent += '</form>';

  // Inject the generated content into the modal body
  document.getElementById('checklist-content').innerHTML = modalContent;

}


function rearrangeFormKeys(obj) {

  // Define the desired order of keys
  const order = ['basic_entry', 'intro', 'questions', 'additional_entry'];

  // Create a new object to hold the rearranged keys
  const rearranged = {};

  // Loop through the desired order and add the keys from the original object
  order.forEach(key => {
      if (key in obj) {
          rearranged[key] = obj[key];
      }
  });

  return rearranged;
  
}

function fillChecklist(modalBody, formContentObj) {

  const checkboxCells = modalBody.querySelectorAll('input.checkbox-cells');
  const textareaCells = modalBody.querySelectorAll('textarea.textarea-cells');

  checkboxCells.forEach(cell => {
    cell.checked = false;

    // Handle the new naming convention with double underscores
    // Extract question ID and option ID from the name attribute (e.g., "q1__o1")
    const nameAttr = cell.getAttribute('name');
    if (nameAttr && nameAttr.includes('__')) {
      const [q, o] = nameAttr.split('__');
      // Check if this checkbox should be checked (only for 'true' values since we only have tick checkboxes)
      if (formContentObj[q] && formContentObj[q][o] === 'true') {
        cell.checked = true;
      }
    } else {
      // Fallback to old logic for backward compatibility
      const [q,o] = cell.id.replace('_tick', '').replace('_cross', '').split('_');
      if (formContentObj[q] && formContentObj[q][o] === 'true' && cell.id.endsWith('_tick')) {
        cell.checked = true;
      } else if (formContentObj[q] && formContentObj[q][o] === 'false' && cell.id.endsWith('_cross')) {
        cell.checked = true;
      }
    }
    cell.disabled = true;
  })

  textareaCells.forEach(cell => {
    cell.value = '';

    // Check if cell has a name attribute with double underscores (new format)
    const nameAttr = cell.getAttribute('name');
    if (nameAttr && nameAttr.includes('__')) {
      const [q, o] = nameAttr.split('__');
      if (formContentObj[q] && formContentObj[q][o]) {
        cell.value = formContentObj[q][o];
      }
    } else if (cell.id.includes('_entry')) {
      // Handle entry fields
      if (formContentObj[cell.id]) {
        cell.value = formContentObj[cell.id];
      }
    } else {
      // Fallback to old logic for backward compatibility
      const [q,o] = cell.id.split('_');
      if (formContentObj[q] && formContentObj[q][o]) {
        cell.value = formContentObj[q][o];
      }
    }
    cell.disabled = true;
  })

}

document.addEventListener('DOMContentLoaded', () => {
  initialize()
})