export function dynamicUnitDropdown(ele_d, ele_u, unitsByDepartment) {

  // console.log(unitsByDepartment)
  const departmentSelect = document.getElementById(ele_d);

  departmentSelect.addEventListener('change', () => {
    const optionDepartment = departmentSelect.value;
    updateUnitDropdown(ele_u, unitsByDepartment, optionDepartment)
  });
  
  // Trigger the change event to populate the unit dropdown on page load
  departmentSelect.dispatchEvent(new Event('change'));
  
}

function updateUnitDropdown(ele_u, unitsByDepartment, optionDepartment) {

  // console.log(`Selected Department: ${optionDepartment}`);
  const unitSelect = document.getElementById(ele_u);
  unitSelect.innerHTML = '';

  if (optionDepartment === 'ALL') {
    const option = document.createElement('option');
    option.value = "ALL";
    option.text = "ALL";
    unitSelect.appendChild(option);

    Object.values(unitsByDepartment).forEach(units => {
      units.sort();
      for (const u of units) {
        if (u === 'ALL') continue;
        const option = document.createElement('option');
        option.value = u;
        option.text = u;
        unitSelect.appendChild(option);
      }
    })

  } else {

    const units = unitsByDepartment[optionDepartment] || [];

    // console.log(units);
    
    units.sort((a, b) => {
      if (a === "ALL") return -1; // Move 'ALL' to the beginning
      if (b === "ALL") return 1; // Move 'ALL' to the beginning
      return 0; // Keep the original order for other items
    });
    for (const u of units) {
      const option = document.createElement('option');
      option.value = u;
      option.text = u;
      unitSelect.appendChild(option);
    }

  }
}


export function alertMsgModal(
  msg, 
  headerTitle="Alert", 
  headerColor="danger",
  footerButtonText="Close",
  footerButtonColor="secondary"
) {
  // get the modal element by id
  const modal = document.getElementById('general-alert-modal');
  const modalHeader = document.getElementById('general-alert-modal-header');
  const modalHeaderTitle = document.getElementById('general-alert-modal-header-title');
  const modalMsg = document.getElementById('general-alert-modal-msg');
  const modalFooterButton = document.getElementById('general-alert-modal-footer-button');

  // set the modal content
  modalHeaderTitle.innerText = headerTitle;
  modalHeader.className = `modal-header text-bg-${headerColor}`;
  modalHeaderTitle.innterText = headerTitle;
  modalMsg.innerText = msg;
  modalFooterButton.innerText = footerButtonText;
  modalFooterButton.className = `btn btn-${footerButtonColor}`;

  // show the modal
  const bootstrapModal = new bootstrap.Modal(modal);
  bootstrapModal.show();
}
