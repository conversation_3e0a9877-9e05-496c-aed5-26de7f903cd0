import { dynamicUnitDropdown } from "./utils.js";


function getDataAttributes() {
    const container = document.getElementById('data-container');
    if (!container) return {};
  
    return {
      unitsByDepartment: JSON.parse(container.dataset.unitsByDepartment),
      testsOverview: JSON.parse(container.dataset.testsOverview)
    };
}

function initialize() {

    const { unitsByDepartment, testsOverview } = getDataAttributes();
    
    switchTabs();
    dynamicUnitDropdown('department','unit', unitsByDepartment);
    listenTestSelect(testsOverview);

}




function switchTabs() {

    const tabs = document.querySelectorAll('.nav-link');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
    
            switch (tab.getAttribute('id')) {
            case 'tab-u':
                var activeForm = document.getElementById('view-by-unit');
                break;
            case 'tab-p':
                var activeForm = document.getElementById('view-by-person');
                break;
            }
    
            const forms = document.querySelectorAll('.tab-pane');
            forms.forEach(f => f.classList.remove('show', 'active'));
            activeForm.classList.add('show','active');
    
        });
    
    });
}


function updateVersionDropdown(ele_t,ele_v, testsOverview) {

    const testSelect = document.getElementById(ele_t);
    const versionSelect = document.getElementById(ele_v);

    const selectedTest = testSelect.value;
    versionSelect.innerHTML = '';

    // if selected test is "ALL"
    // version is set to "ALL" and disabled
    // then early return this function
    if (selectedTest == 'ALL') {
        const option = document.createElement('option');
        option.value = "ALL";
        option.text = "ALL";
        versionSelect.appendChild(option);
        return
    }


    const versions = testsOverview[selectedTest] || [];

    const sortedKeys = Object.keys(versions).sort(
        (a, b) => b.localeCompare(a)
    );

    const sortedVersions = {};
    for (let key of sortedKeys) {
        sortedVersions[key] = versions[key];
    }
        
    for (let v in sortedVersions) {
        if (sortedVersions.hasOwnProperty(v)) {
        const option = document.createElement('option');
        option.value = sortedVersions[v]['version'];
        option.text = `${sortedVersions[v]['version']} - ${sortedVersions[v]['version_name']}`;
        versionSelect.appendChild(option);
        }
    };
}

function listenTestSelect(testsOverview) {

    const ele_tests = [['test','version'],['test_person','version_person']]
    ele_tests.forEach(ele => {
        const testSelect = document.getElementById(ele[0]);
        testSelect.addEventListener('change', () => {updateVersionDropdown(ele[0], ele[1], testsOverview);});
        // Trigger the change event to populate the email dropdown on page load
        testSelect.dispatchEvent(new Event('change'));
    })
    
    
}


document.addEventListener('DOMContentLoaded', () => {
    initialize()
})