<!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-aFq/bzH65dt+w6FI2ooMVUpc+21e0SRygnTpmBvdBgSdnuTN7QbdgL+OapgHtvPp"
      crossorigin="anonymous"
    />

<!-- Popperjs -->
    <script
      src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"
      crossorigin="anonymous"
    ></script>

<table border="1">
  <tr>
    <td rowspan="2">Merged across 2 rows</td>
    <td>Row 1, Cell 2</td>
  </tr>
  <tr>
    <!-- This cell is covered by the rowspan above -->
    <td>Row 2, Cell 2</td>
  </tr>
</table>

<hr>

<table border="1">
  <tr>
    <td colspan="2">Merged across 2 columns</td>
  </tr>
  <tr>
    <td>Row 2, Cell 1</td>
    <td>Row 2, Cell 2</td>
  </tr>
</table>

<hr>

<table border="1">
  <tr>
    <td>
      Outer Cell
      <table border="1" style="width: 100%">
        <tr>
          <td>Inner Cell 1</td>
          <td>Inner Cell 2</td>
        </tr>
      </table>
    </td>
    <td>Normal Cell</td>
  </tr>
</table>

<hr>

<table border="1">
  <tr>
    <th rowspan="2">Category</th>
    <th colspan="2">Details</th>
  </tr>
  <tr>
    <!-- Category cell is merged from above -->
    <th>Item</th>
    <th>Price</th>
  </tr>
  <tr>
    <td rowspan="2">Fruits</td>
    <td>
      <table border="1" style="width: 100%">
        <tr><td>Apple</td><td>Red</td></tr>
      </table>
    </td>
    <td>$1.00</td>
  </tr>
  <tr>
    <!-- Fruits cell is merged from above -->
    <td>Banana</td>
    <td>$0.50</td>
  </tr>
</table>

<td>
  <div style="display: flex;">
    <div style="flex: 1; border-right: 1px solid #ccc;">Split 1</div>
    <div style="flex: 1;">Split 2</div>
  </div>
</td>


<table class="table table-bordered text-center align-middle">
    <thead class="table-light">
      <tr>
        <th colspan="4">Product Catalog</th>
      </tr>
      <tr>
        <th rowspan="2">Category</th>
        <th colspan="2">Details</th>
        <th rowspan="2">Split Cell (Flex)</th>
      </tr>
      <tr>
        <th>Item</th>
        <th>Price</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>Fruits</td>
        <td>Apple</td>
        <td>$1.00</td>
        <td class="p-0">
          <div class="container-fluid">
            <div class="row g-0">
              <div class="col-6 border-end py-2 text-center">Red</div>
              <div class="col-6 py-2 text-center">Green</div>
            </div>
          </div>
        </td>
      </tr>
      <tr>
        <td></td>
        <td>Banana</td>
        <td>$0.50</td>
        <td class="p-0">
          <div class="container-fluid">
            <div class="row g-0">
              <div class="col-6 border-end py-2 text-center">Ripe</div>
              <div class="col-6 py-2 text-center">Raw</div>
            </div>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
