{% extends 'base.html' %} 
{% block custom_head %}
{% endblock %} 
{% block bodyTop %}
<div class="container">
  <div
    class="alert alert-dismissible fade show"
    role="alert"
    id="flash-message-add-staff"
    style="display: none"
  ></div>
</div>
{% endblock %} 

{% block content %}
<div class="container mt-4">
  <form id="add-form" method="POST">
    <input type="hidden" name="update" id="update" value="false">
    <div class="form-group mt-3">
      <label 
        class="form-label"
      >Discipline:</label>
      {# radio outline buttons of Nurse and PCA #}
      <input type="radio" class="btn-check" name="employee_discipline" id="discipline-nurse"  value="nurse" autocomplete="off" checked>
      <label class="btn btn-outline-success" for="discipline-nurse">Nurse</label>
      <input type="radio" class="btn-check" name="employee_discipline" id="discipline-pca" value="pca" autocomplete="off">
      <label class="btn btn-outline-warning" for="discipline-pca">PCA</label>
    </div>
    <div class="form-group mt-3">
      <label for="employee-name">Employee Name:</label>
      <input
        type="text"
        class="form-control"
        id="employee-name"
        name="employee_name"
        required
      />
    </div>
    <div class="form-group mt-3">
      <label for="employee-no">Employee No:</label>
      <input
        type="text"
        class="form-control"
        id="employee-no"
        name="employee_no"
        required
      />
    </div>
    <div class="form-group mt-1">
      <input 
        class="form-check-input temp-staff-display" 
        type="checkbox" 
        id="from-temp" 
        name="from_temp"
        value="false"
      />
      <label class="form-check-label temp-staff-display" for="from-temp">Convert from TEMP staff</label>
      <select class="form-select" id="temp-staff" name="temp_staff" style="display: none;">
        <option value="" id="temp-staff-option-default">Select TEMP staff</option>
        {% for temp_code, staff in temp_staff.items() %}
          <option 
            value="{{ temp_code }}"
            id="temp-staff-option-{{  temp_code }}"
            data-temp-staff-name="{{ staff['Name'] }}"
            data-temp-staff-department="{{ staff['Department'] }}"
            data-temp-staff-unit="{{ staff['Unit'] }}"
          >{{ temp_code }} : {{ staff['Name'] }}</option>
        {% endfor %}
      </select>
    </div>
    <div class="form-group mt-3">
      <label for="department">Department:</label>
      <select class="form-select" id="department" name="employee_department" required>
        {% for department in allowed_departments %}
          <option value="{{ department }}">{{ department }}</option>
        {% endfor %}
      </select>
    </div>
    <div class="form-group mt-3">
      <label for="unit">Unit:</label>
      <select class="form-select" id="unit" name="employee_unit" required></select>
    </div>
    <button 
      type="button" 
      class="btn btn-primary mt-3" 
      id="submit-btn"
      {# data-bs-toggle="modal"
      data-bs-target="#confirm-add-staff-modal" #}
    >Submit</button>
    <button
      type="button"
      class="btn btn-secondary mt-3 ms-3"
      onclick="location.href='{{ url_for('ui') }}';"
    >
      Back
    </button>
  </form>
</div>

{# <button
  type="button"
  id="trigger-modal"
  class="btn btn-primary"
  data-toggle="modal"
  data-target="#confirm-add-staff-modal"
  hidden
></button> #}
<div
  class="modal fade"
  id="confirm-add-staff-modal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="confirm-add-staff-modal-label"
  aria-hidden="false"
>
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header" id="confirm-add-staff-modal-header">
        <h5 class="modal-title" id="confirm-add-staff-modal-label"></h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <h2 id="modal-body-title"></h2>
        <form class="mt-3" id="add-staff-form-dialog" method="POST">
          <div class="mb-3">
            <div class='d-flex justify-content-between'>
              <label 
                class="form-label"
              >Name:</label>
              <span 
                class="text-secondary"
                id="current-employee-name"
              ></span>
            </div>
            <input
              type="text"
              class="form-control"
              id="confirm-employee-name"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <div class='d-flex justify-content-between'>
              <label 
                class="form-label" 
              >Employee No:</label>
              <span 
                class="text-secondary"
                id="current-employee-no"
              ></span>
            </div>
            <input
              type="text"
              class="form-control"
              id="confirm-employee-no"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <div class='d-flex justify-content-between'>
              <label 
                class="form-label"
              >Department:</label>
              <span 
                class="text-secondary"
                id="current-employee-department"
              ></span>
            </div>
            <input
              type="text"
              class="form-control"
              id="confirm-employee-department"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <div class='d-flex justify-content-between'>
              <label 
                class="form-label"
              >Unit:</label>
              <span 
                class="text-secondary"
                id="current-employee-unit"
              ></span>
            </div>
            <input
              type="text"
              class="form-control"
              id="confirm-employee-unit"
              value=""
              readonly
            />
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" data-bs-dismiss="modal" id="confirm-btn">
          Confirm
        </button>
      </div>
    </div>
  </div>
</div>

<div 
  id="data-container"
  data-units-by-department='{{ units_by_department | tojson | safe }}'
  data-all-staff='{{ all_staff | tojson | safe }}'
  data-temp-staff='{{ temp_staff | tojson | safe }}'
></div>

<script type="module" src="{{ url_for('static', filename='js/add_staff.js') }}"></script>

{% endblock %}
