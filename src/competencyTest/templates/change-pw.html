{% extends 'base.html' %} {% block custom_head %} {% endblock %} {% block
bodyTop %}
<div class="container">
  <div
    class="alert alert-dismissible fade show"
    role="alert"
    id="flash-message-chgPW"
    style="display: none"
  ></div>
</div>
{% endblock %} {% block content %}
<div class="container mt-4">
  <form id="chgPW-form" method="POST">
    <div class="form-group mt-3">
      <label for="newPW">New Password:</label>
      <input
        type="password"
        name="newPW"
        class="form-control"
        id="newPW"
        required
      />
    </div>
    <div class="form-group mt-3">
      <label for="cfmPW">Confirm New Password:</label>
      <input
        type="password"
        name="cfmPW"
        class="form-control"
        id="cfmPW"
        required
      />
    </div>
    <button type="button" class="btn btn-success mt-3" id="submit-btn">
      Submit
    </button>
    <button
      type="button"
      class="btn btn-secondary mt-3 ms-3"
      onclick="location.href='{{ url_for('ui') }}';"
    >
      Back
    </button>
  </form>
</div>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const form = document.querySelector("#chgPW-form");

    const submitBtn = document.querySelector("#submit-btn");
    submitBtn.addEventListener("click", (e) => {
      e.preventDefault();

      // Get user input
      const newPW = document.querySelector("#newPW").value;
      const cfmPW = document.querySelector("#cfmPW").value;

      if (newPW !== cfmPW) {
        flash("flash-message-chgPW", "NEW Password not consistent.", "danger");
        return;
      }

      form.submit();
    });
  });
</script>

{% endblock %}
