{% extends 'base.html' %} 

{% block custom_head %}

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

{% endblock %}

{% block content %}
<style>
  .card-header {
    background-color: #c9e1bf; /* Replace with the desired background color */
  }

  .question-text {
    color: #1e159d;
  }

  table,textarea {
    width: 100%;
  }

  .item-content,label {
    white-space: pre-wrap;
  }

  #item-col {
    min-width: 400px;
    width: 60%;
  }

  .h-center {
    text-align: center;
  }

  .v-center {
    vertical-align: middle;
  }

  .bold-large {
    font-weight: bold;
    font-size: large;
  }

</style>

<h1>{{ test_name|replace("_", " ") }}</h1>
<h1>{{ checklist_content.get('intro', {}).get('header', '') }}</h1>
{% if checklist_info.version_name %}
<h5 style="color: #9d9d9d">({{ checklist_info.version }} - {{ checklist_info.version_name }})</h5>
{% else %}
<h5 style="color: #9d9d9d">({{ checklist_info.version }})</h5>
{% endif %}
<div class="container-fluid mx-1">
  <form id="complete-form" action="" method="post">

    <input type="hidden" name="test_name" value="{{ test_name }}">
    <input type="hidden" name="test_version" value="{{ checklist_info.version }}">

    <div class="card mt-5">
      <div class="card-header">
        <h3>Staff Info</h3>
      </div>
      <div class="card-body">
        <div class="form-group mt-3">
          <label for="department">Department:</label>
          <select class="form-select" name="department" id="department">
            {% for dept in departments %}
            <option value="{{ dept }}">{{ dept }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="unit">Unit:</label>
          <select class="form-select" name="unit" id="unit">
            <!-- Unit options will be populated using JavaScript -->
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="name">Name:</label>
          <input
            class="form-control"
            type="text"
            name="name"
            id="name"
            required
          />
        </div>
        <div class="form-group mt-3">
          <label for="corpid">Employee No:</label>
          <input
            class="form-control"
            type="number"
            name="employeeno"
            id="employeeno"
            required
          />
        </div>
      </div>
    </div>

    {% for part,page in checklist_content.items() %}
    {% if page.get('details', {}) %}
    <div class="card mt-5">
      <div class="card-header">
        <h3>{{ page.header }}</h3>
      </div>
      <div class="card-body">
        <div class="form-group">
          {% if part == 'intro' %}
          <label 
            class="mt-3 mb-3" 
          >{{ page.details|safe }}</label>

          {% elif part == 'questions' %}

            <div class="table-responsive">
              <table class="table table-striped table-bordered table-hover" id="checklist-content">
                <thead>
                  <tr>
                    <th></th>
                    <th class="h-center v-center bold-large">{{ page.itemcol }}</th>
                    {% for k,opt in page.details.options.items() %}
                      {% if opt.type == 'checkbox' %}
                        <th>
                          <div 
                            class="d-flex flex-column align-items-center bold-large"
                            {% if opt.count_score %}
                              data-cs="true"
                            {% else %}
                              data-cs="false"
                            {% endif %}
                          >
                            {{ opt.name }}
                            <button 
                              type="button" 
                              class="btn btn-success mt-2 check-all"
                              data-column="col-{{ k }}"
                            >Check ALL</button>
                            <button
                              type="button"
                              class="btn btn-primary mt-2 reset-all"
                              data-column="col-{{ k }}"
                            >Reset ALL</button>
                          </div>
                        </th>
                      {% else %}
                        <th class="h-center v-center bold-large">{{ opt.name }}</th>
                      {% endif %}
                    {% endfor %}
                  </tr>
                </thead>
                <tbody>
                  {% for id,item in page.details.get('items',{}).items() %}
                    <tr>
                      {% if item.get('subtitle','') == 1 %}

                        {% if item.essential %}
                          <td style="color: red; font-weight: bold" data-essential="true">
                            <p>{{ id | regex_replace("^q0?", "") | replace('_', '.') }}*</p>
                          </td>
                        {% else %}
                          <td>
                            <p>{{ id | regex_replace("^q0?", "") | replace('_', '.') }}</p>
                          </td>
                        {% endif %}

                        <td colspan="{{ page.details.options|length + 1 }}">
                          <p>{{ item.text|safe }}</p>
                        </td>

                      {% elif item.get('subtitle','') == 2 %}

                        {% if item.essential %}
                          <td style="color: red; font-weight: bold;" data-essential="true">
                            <p><i>{{ id | regex_replace("^q0?", "") | replace('_', '.') }}*</i></p>
                          </td>
                        {% else %}
                          <td>
                            <p><i>{{ id | regex_replace("^q0?", "") | replace('_', '.') }}</i></p>
                          </td>
                        {% endif %}

                        <td colspan="{{ page.details.options|length + 1 }}">
                          <i>{{ item.text|safe }}</i>
                        </td>

                      {% else %}

                        {% if item.get('subtitle','') == 3 %}
                          <td></td>
                          <td class="p-0">
                            <div class="container-fluid p-0">
                              <div class="row g-0">
                                <div class="col-3 border-end py-2 px-1">
                                  {% if item.essential %}
                                    <span style="color: red; font-weight: bold" data-essential="true">{{ id | regex_replace("^q0?", "") | replace('_', '.') }}*</span>
                                  {% else %}
                                    <span>{{ id | regex_replace("^q0?", "") | replace('_', '.') }}</span>
                                  {% endif %}
                                </div>
                                <div class="col-9 py-2 px-1 item-content" data-essential="false">{{ item.text|safe }}</div>
                              </div>
                            </div>
                          </td>
                          
                        {% else %}

                          {% if item.essential %}
                            <td style="color: red; font-weight: bold" data-essential="true">{{ id | regex_replace("^q0?", "") | replace('_', '.') }}*</td>
                          {% else %}
                            <td>{{ id | regex_replace("^q0?", "") | replace('_', '.') }}</td>
                          {% endif %}

                          <td class="item-content" data-essential="false">{{ item.text|safe }}</td>

                        {% endif %}

                        {% for k,opt in page.details.options.items() %}

                          {% if opt.type == 'checkbox' %}
                            <td>
                              <div>
                                <input 
                                  type="radio"
                                  class="btn-check checklist-options {{ id }}_options col-{{ k }}" 
                                  data-options="{{ id }}_options"
                                  id="{{ id }}_{{ k }}_tick" 
                                  value="true"
                                  autocomplete="off" 
                                  name="{{ id }}__{{ k }}" 
                                /> {# Use double underscore to separate question id and option id #}
                                <label 
                                  class="btn btn-outline-success"
                                  for="{{ id }}_{{ k }}_tick"
                                ><i class="fa-solid fa-check"></i></label>
                              </div>
                              
                              <button
                                type="button"
                                class="btn btn-outline-primary mt-2 col-{{ k }} reset-button"
                                value="reset"
                              >Reset</button>
                            </td>
                          {% else %}
                            <td class="h-center">
                              <textarea 
                                class="form-control" 
                                name="{{ id }}__{{ k }}"> {# Use double underscore to separate question id and option id #}
                              </textarea>
                            </td>
                          {% endif %}
                        
                        {% endfor %}
                      {% endif %}
                    </tr>
                  {% endfor %}

                  {% if true in (page.details.options.values() | map(attribute='count_score') ) %}
                  <tr>
                    <td></td>
                    <td>
                      {# large bold text "TOTAL SCORE" #}
                      <span class="bold-large">TOTAL SCORE: </span>
                      <span class="bold-large" id="total-score">--</span>
                      <input type="hidden" name="total-score" id="total-score-input" value="0">
                    </td>
                    <td>
                      <span class="bold-large" id="pass-fail">--</span>
                      <input type="hidden" name="pass-fail" id="pass-fail-input" value="fail">
                    </td>

                  </tr>
                  {% endif %}
                </tbody>
              </table>
            </div>

          {% else %}
          {# parts other than intro and questions #}

          {% for k, item in page['details'].items() %}

            {% if item.input == 'department_and_unit' %}
              <div class="form-group input-group mt-3 mb-3">
                <span
                  class="input-group-text"
                >{{ item.title|safe }}</span>
                <div class="form-group mx-2">
                  {# <label for="{{part}}_{{ '{:02}'.format(loop.index) }}_d">{{item.title}}</label> #}
                  <select class="form-control form-select select-dept" name="{{k}}_d" id="{{k}}_d">
                    {% for dept in departments %}
                    <option value="{{ dept }}">{{ dept }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="form-group">
                  {# <label for="{{part}}_{{ '{:02}'.format(loop.index) }}_u"> </label> #}
                  <select class="form-control form-select select-unit" name="{{k}}_u" id="{{k}}_u">
                  <!-- generated by javascript -->
                  </select>
                </div>

              </div>

            {% else %}

              {% if item.input is not string %}
                <span>{{ item.title|safe }}:</span>
                <select
                  class="form-control form-select" 
                  name="{{ k }}"
                >
                  <option value="">-- Please Select --</option>
                  {% for opt in item.input %}
                  <option value="{{ opt }}">{{ opt }}</option>
                  {% endfor %}
                </select>
              {% else %}
              <div class="form-group input-group mt-3 mb-3">
                <span
                  class="input-group-text"
                >{{ item.title|safe }}</span>
                <input 
                  class="form-control entry-input"
                  name="{{ k }}"
                  data-input-type="{{ item.input }}"
                  {% if item.get('required', False) %}
                  required
                  {% endif %}
                />
              </div>
              {% endif %}

            {% endif %}
            {# end of input types #}

          {% endfor %}

          {% endif %}
          {# end of all parts #}

        </div>
      </div>
    </div>
    {% endif %}
    {% endfor %}

    <button type="submit" id="form-submit" class="btn btn-success mt-5 mb-5">Submit</button>

  </form>
</div>


<button type="button" id="trigger-modal" class="btn btn-primary" data-toggle="modal" data-target="#confirm-staff-modal" hidden></button>
<div class="modal fade" id="confirm-staff-modal" tabindex="-1" role="dialog" aria-labelledby="confirm-syaff-modal-label" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="confirm-staff-modal-label">Confirm Staff</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <h1 class="mb-3" id="found-staff">Found Existing Staff</h1>
        <div class="mb-3">
          <label for="modal-staff-employeeno" class="form-label">Employee No.:</label>
          <input type="text" class="form-control" id="modal-staff-employeeno" value="" readonly>
        </div>
        <div class="mb-3">
          <label for="modal-staff-name" class="form-label">Name:</label>
          <input type="text" class="form-control" id="modal-staff-name" value="" readonly>
        </div>
        <div class="mb-3">
          <label for="modal-staff-dept" class="form-label">Department:</label>
          <input type="text" class="form-control" id="modal-staff-dept" value="" readonly>
        </div>
        <div class="mb-3">
          <label for="modal-staff-unit" class="form-label">Unit:</label>
          <input type="text" class="form-control" id="modal-staff-unit" value="" readonly>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="confirm-staff-btn">Confirm</button>
      </div>
    </div>
  </div>
</div>

<!-- Add a container with data attributes -->
<div 
  id="data-container"
  data-pcas='{{ pcas | tojson | safe }}'
  data-temp-staff='{{ temp_staff | tojson | safe }}'
  data-nurses='{{ nurses | tojson | safe }}'
  data-units-by-department='{{ units_by_department | tojson | safe }}'
  data-passing-criteria='{{ passing_criteria | tojson | safe }}'
  data-checklist-info='{{ checklist_info | tojson | safe }}'
  data-ranks='{{ ranks | tojson | safe }}'
  data-is-pca-assessment-form="{{ is_pca_assessment_form }}"
></div>

<script type="module" src="{{ url_for('static', filename='js/checklist_form.js') }}"></script>

<script>

{# $(document).ready(function() {
    $('.with-search').select2();
}); #}

</script>



{% endblock %}
