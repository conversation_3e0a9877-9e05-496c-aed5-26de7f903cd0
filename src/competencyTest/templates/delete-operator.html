{% extends 'base.html' %} {% block custom_head %} {% endblock %} {% block
bodyTop %}
<div class="container">
  <div
    class="alert alert-dismissible fade show"
    role="alert"
    id="flash-message-delete"
    style="display: none"
  ></div>
</div>
{% endblock %} {% block content %}
<div class="container mt-4">
  <form id="delete-form" method="POST">
    <div class="form-group mt-3">
      <label for="corp_id">CORP ID:</label>
      <input
        type="text"
        name="corp_id"
        class="form-control"
        id="corp_id"
        required
      />
    </div>
    <button type="button" class="btn btn-danger mt-3" id="submit-btn">
      Delete
    </button>
    <button
      type="button"
      class="btn btn-secondary mt-3 ms-3"
      onclick="location.href='{{ url_for('ui') }}';"
    >
      Back
    </button>
  </form>
</div>

<!-- Add this inside your <body> tag -->
<button
  type="button"
  id="trigger-modal"
  class="btn btn-primary"
  data-toggle="modal"
  data-target="#confirm-delete-modal"
  hidden
></button>
<div
  class="modal fade"
  id="confirm-delete-modal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="confirm-delete-modal-label"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="confirm-delete-modal-label">
          Confirm Delete
        </h5>
        <button
          type="button"
          class="close"
          data-bs-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="delete-form-dialog" method="POST">
          <div class="mb-3">
            <label for="input-corp-id" class="form-label">CORP ID:</label>
            <input
              type="text"
              class="form-control"
              id="input-corp-id"
              name="corp_id"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <label for="input-corp-id" class="form-label">Last Name:</label>
            <input
              type="text"
              class="form-control"
              id="last-name"
              name="last_name"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <label for="input-corp-id" class="form-label">First Name:</label>
            <input
              type="text"
              class="form-control"
              id="first-name"
              name="first_name"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <label for="input-department" class="form-label">Department:</label>
            <input
              type="text"
              class="form-control"
              id="input-department"
              name="department"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <label for="input-unit" class="form-label">Unit:</label>
            <input
              type="text"
              class="form-control"
              id="input-unit"
              name="unit"
              value=""
              readonly
            />
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-danger" id="confirm-delete-btn">
          Confirm
        </button>
      </div>
    </div>
  </div>
</div>

<script>


  document.addEventListener('DOMContentLoaded', () => {
    const form = document.querySelector('#delete-form');
    const confirmBtn = document.querySelector('#confirm-delete-btn');

    confirmBtn.addEventListener('click', () => {
      // Submit the form when the user confirms
      form.submit();
    });

    const submitBtn = document.querySelector('#submit-btn');
    submitBtn.addEventListener('click', (e) => {

      e.preventDefault();

      // Get user input
      const corpId = document.querySelector('#corp_id').value;

      // Get Last Name and First Name
      const operatorName = {{ operator_name|safe }};
      console.log(operatorName)
      const foundOp = Object.entries(operatorName).find(([key, value]) => key === corpId);
      var d = ''
      var u = ''
      var lastName = ''
      var firstName = ''
      var idFound = false

      if (foundOp) {
        d = foundOp[1]['Department']
        u = foundOp[1]['Unit']
        lastName = foundOp[1]['Last_Name']
        firstName = foundOp[1]['First_Name']
        idFound = true
        console.log('CORP ID found.');
      } else {
        flash('flash-message-delete','CORP ID not found','danger');
      };

      if (!idFound) { return; };

      // Populate the form with user input
      document.querySelector('#input-corp-id').value = corpId;
      document.querySelector('#last-name').value = lastName;
      document.querySelector('#first-name').value = firstName;
      document.querySelector('#input-department').value = d;
      document.querySelector('#input-unit').value = u;

      console.log('Next Step: Load Modal...');

      // Display the confirmation dialog
      const modal = new bootstrap.Modal(document.querySelector('#confirm-delete-modal'));
      modal.show();

    });

  });
</script>

{% endblock %}
