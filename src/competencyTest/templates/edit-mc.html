{% extends 'base.html' %} 

{% block content %}
<style>
  .card-header {
    background-color: #c9e1bf; /* Replace with the desired background color */
  }

  .question-text {
    color: #1e159d;
  }
</style>

<h1>Fall Prevention Quiz</h1>
<h5>{{ fall_test_info.version }} - {{ fall_test_info.name }} </h5>
<div class="container">
  <form id="edit-mc" action="" method="post">

    {% for part,page in fall_test_form.items() %}
    <div class="card mt-5">
      <div class="card-header">
        <input
          class="form-control"
          type="text"
          name="{{part}}_header"
          id="{{part}}_header"
          value="{{ page.header }}" 
        />
      </div>
      <div class="card-body">
        <div class="form-group">
          {% if part == 'intro' %}
          <textarea 
            class=" form-control mt-3 mb-3" 
            id="intro_details"
            name="intro_details"
            rows="5"
            required
          >{{ page.details|safe }}</textarea>
          {% else %}
          {% for id,item in page.details.items() %}
          <div class="question-container">
            <div
              class="d-flex justify-content-between mb-3"  
            >
              <h3 class="mt-3">{{ id }}</h3>
              <button 
                type="button" 
                class="btn btn-danger mt-3 mb-3 del-Q"
              >
                Delete Question
              </button>
            </div>
            <textarea
              class="question-text mt-3 mb-3 form-control"
              id="{{ id }}"
              name="{{ id }}"
              rows="5"
            >{{ item.q|safe }}</textarea>
            {% for option in item.opt %}
            <div class="input-group option-container">
              <input
                class="form-control mb-2"
                type="text"
                name="{{ id }}_{{ loop.index }}"
                id="{{ id }}_{{ loop.index }}"
                value="{{ option }}"
                required
              />
              <button
                type="button"
                class="btn btn-secondary mb-2 del-O"
              >
                <i class="fa-solid fa-trash" style="color: #ffffff;"></i>
              </button>
            </div>
            {% endfor %} 

            <button
              type="button" 
              class="btn btn-primary mb-3 new-O"
              data-id="{{ id }}"
            >
              Add New Option
            </button>

            
            <!-- Add the horizontal line -->
            <hr />

          </div>

          {% endfor %}

          <div
            class="d-flex justify-content-center mb-3"
          >
            <button
              type="button" 
              class="btn btn-warning mt-3 mb-3 new-Q"
            >
              Add New Question
            </button>
          </div>

          {% endif %}
        </div>
      </div>
    </div>
    {% endfor %}
    <button type="button" class="btn btn-danger mt-5 mb-5">Save</button>
    <button type="button" name="saveAsNew" id="saveAsNew" class="btn btn-success mt-5 mb-5 ms-2">Save as New</button>
  </form>
</div>

<script>

function removeQuestionContainer() {
  // Get the button element that was clicked
  const button = $(this);

  // Traverse up the DOM tree to find the ancestor div with class "question-container"
  const questionContainer = button.closest('.question-container');

  // Remove the question container from the DOM
  questionContainer.remove();
}

function removeOptionContainer() {
  // Get the button element that was clicked
  const button = $(this);

  // Traverse up the DOM tree to find the ancestor div with class "option-container"
  const optionContainer = button.closest('.option-container');

  // Remove the option container from the DOM
  optionContainer.remove();

}

function addNewOption() {

  // Get the button element that was clicked
  const button = $(this);
  const id = button.attr('data-id');

  // Traverse up the DOM tree to find the ancestor div with class "question-container"
  const questionContainer = button.closest('.question-container');

  // Get the number of existing options
  const optionCount = questionContainer.find('.option-container').length;

  // Create the new option container
  const newOptionContainer = $('<div>').addClass('input-group option-container');

  // Create the input element for the option
  const newOptionInput = $('<input>')
    .addClass('form-control mb-2')
    .attr('type', 'text')
    .attr('name', `${id}_${optionCount + 1}`)
    .attr('id', `${id}_${optionCount + 1}`)
    .attr('required', true);

  // Create the delete button for the option
  const deleteButton = $('<button>')
    .addClass('btn btn-secondary mb-2 del-O')
    .attr('type', 'button')
    .html('<i class="fa-solid fa-trash" style="color: #ffffff;"></i>')
    .on('click', removeOptionContainer);

  // Append the input and delete button to the new option container
  newOptionContainer.append(newOptionInput, deleteButton);

  // Insert the new option container after the last option
  const lastOptionContainer = questionContainer.find('.option-container').last();
  newOptionContainer.insertAfter(lastOptionContainer);
}

function addNewQuestion() {
  // Get the last question container
  const lastQuestionContainer = $('.question-container').last();

  // Get the ID for the new question
  const lastQuestionId = lastQuestionContainer.find('h3').text();

  // Extract the number from the ID and increment it by 1
  const newQuestionNumber = parseInt(lastQuestionId.slice(1)) + 1;
  const newQuestionId = 'q' + newQuestionNumber.toString().padStart(2, '0')

  // Create the new question container
  const newQuestionContainer = $('<div>').addClass('question-container');

  // Create the heading for the new question
  const newQuestionHeading = $('<div>').addClass('d-flex justify-content-between mb-3');
  const newQuestionHeadingText = $('<h3>').addClass('mt-3').text(newQuestionId);
  const newQuestionDeleteButton = $('<button>')
    .addClass('btn btn-danger mt-3 mb-3 del-Q')
    .attr('type', 'button')
    .text('Delete Question')
    .on('click', removeQuestionContainer);

  // Append the heading and delete button to the new question container
  newQuestionHeading.append(newQuestionHeadingText, newQuestionDeleteButton);
  newQuestionContainer.append(newQuestionHeading);

  // Create the textarea for the new question
  const newQuestionTextArea = $('<textarea>')
    .addClass('question-text mt-3 mb-3 form-control')
    .attr('id', newQuestionId)
    .attr('name', newQuestionId)
    .attr('rows', 5);

  // Append the textarea to the new question container
  newQuestionContainer.append(newQuestionTextArea);

  // Create the default options for the new question
  for (let i = 0; i < 4; i++) {
    const newOptionContainer = $('<div>').addClass('input-group option-container');
    const newOptionInput = $('<input>')
      .addClass('form-control mb-2')
      .attr('type', 'text')
      .attr('name', `${newQuestionId}_${i + 1}`)
      .attr('id', `${newQuestionId}_${i + 1}`)
      .attr('required', true);
    const newOptionDeleteButton = $('<button>')
      .addClass('btn btn-secondary mb-2 del-O')
      .attr('type', 'button')
      .html('<i class="fa-solid fa-trash" style="color: #ffffff;"></i>')
      .on('click', removeOptionContainer);
    newOptionContainer.append(newOptionInput, newOptionDeleteButton);
    newQuestionContainer.append(newOptionContainer);
  }

  // Create the "Add New Option" button for the new question
  var newOptionButton = $('<button>')
    .addClass('btn btn-primary mb-3 new-O')
    .attr('data-id', newQuestionId)
    .attr('type', 'button')
    .text('Add New Option')
    .on('click', addNewOption);

  // Append the "Add New Option" button to the new question container
  newQuestionContainer.append(newOptionButton);

  newQuestionContainer.append($('<hr />'));

  // Insert the new question container after the last question container
  newQuestionContainer.insertAfter(lastQuestionContainer);

}

function submitNewForm() {
  document.getElementById('edit-mc').submit();
}

$(document).ready(function() {
  // Attach the click event handler to the "Delete Question" button
  $('.del-Q').on('click', removeQuestionContainer);

  // Attach the click event handler to the "Delete Option" button
  $('.del-O').on('click', removeOptionContainer);

  // Attach the click event handler to the "Add New Option" button
  $('.new-O').on('click', addNewOption);

  // Attach the click event handler to the "Add New Question" button
  $('.new-Q').on('click', addNewQuestion);


  // Attach the click event handler to the "Save As New" button
  $('#saveAsNew').on('click', submitNewForm);

});
  
</script>

{% endblock %}
