{% extends 'base.html' %} {% block content %}

<div class="container">
  <h1>Add/Edit Unit</h1>
  <!-- Nav tabs -->
  <ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
      <a
        class="nav-link {{ form_status['a'][0] }}"
        data-toggle="tab"
        href="#add-unit"
        id="tab-a"
        role="tab"
        >ADD</a
      >
    </li>
    <li class="nav-item">
      <a
        class="nav-link {{ form_status['e'][0] }}"
        data-toggle="tab"
        href="#edit-unit"
        id="tab-e"
        role="tab"
        >EDIT</a
      >
    </li>
  </ul>

  <!-- Tab panes -->
  <div class="tab-content mt-3">
    <div
      class="tab-pane fade {{ form_status['a'][1] }}"
      id="add-unit"
      role="tabpanel"
    >
      <form action="" method="POST" id="add-unit-form">
        <div class="form-group mt-3 border border-info rounded p-3">
          <label for="a-department">Select Department:</label>
          <select name="a-department" id="a-department" class="form-select">
            {% for dept in allowed_departments %}
            <option value="{{ dept }}">{{ dept }}</option>
            {% endfor %}
          </select>
        
          <label for="dept_other" class="mt-2">If ADD NEW, please specify :</label>
          <input type="text" name="dept_other" id="dept_other" class="form-control other-input" required disabled>
        </div>
        <div class="form-group mt-3">
          <label for="a-unit">Unit to Add:</label>
          <input type="text" name="a-unit" id="a-unit" class="form-control other-input" required>
        </div>
        <div class="form-group mt-3">
          <label for="a-email">Email:</label>
          <input type="text" name="a-email" id="a-email" class="form-control other-input" required>
        </div>
        <div class="form-group mt-3">
          <label for="a-password">Password:</label>
          <input type="text" name="a-password" id="a-password" class="form-control other-input" required>
        </div>
        <div class="form-group mt-3 border border-info rounded p-3">
          <label for="add-access">Access:</label>
          <input type="text" name="add-access" id="add-access" class="form-control other-input" required>

          <label for="add-access-department" class="mt-2">Choose Department to Add:</label>
          <div class="input-group">
            <select id="add-access-department" class="form-select">
              <option value="ALL">ALL</option>
              {% for dept in allowed_departments %}
              {% if dept != '[ ADD NEW ]' %}
              <option value="{{ dept }}">{{ dept }}</option>
              {% endif %}
              {% endfor %}
            </select>
            <button type="button" id="addAccessButton" class="btn btn-warning">ADD</button>
          </div>
        </div>
        <div class="form-group mt-3">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="invisible-to-public" id="invisible-to-public">
            <label class="form-check-label" for="invisible-to-public">Invisible to public</label>
          </div>
        </div>
        <button type="submit" class="btn btn-success mt-3" name="submit-a">
          Submit
        </button>
      </form>
    </div>

    <!-- In the edit-unit tab pane -->
    <div
      class="tab-pane fade {{ form_status['e'][1] }}"
      id="edit-unit"
      role="tabpanel"
    >
      <form action="" method="POST" id="edit-unit-form">

        <div class="form-group mt-3">
          <label for="e-department">Select Department:</label>
          <select name="e-department" id="e-department" class="form-select">
            {% for dept in allowed_departments %}
            {% if dept != '[ ADD NEW ]' %}
            <option value="{{ dept }}">{{ dept }}</option>
            {% endif %}
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="e-unit">Select Unit:</label>
          <select name="e-unit" id="e-unit" class="form-select">
            <!-- Unit options will be populated using JavaScript -->
          </select>
        </div>

        <div class="form-group mt-3 border border-info rounded p-3">
          <label for="edit-access">Access:</label>
          <input type="text" name="edit-access" id="edit-access" class="form-control other-input" required>

          <label for="edit-access-department" class="mt-2">Choose Department to Add:</label>
          <div class="input-group">
            <select id="edit-access-department" class="form-select">
              <option value="ALL">ALL</option>
              {% for dept in allowed_departments %}
              {% if dept != '[ ADD NEW ]' %}
              <option value="{{ dept }}">{{ dept }}</option>
              {% endif %}
              {% endfor %}
            </select>
            <button type="button" id="editAccessButton" class="btn btn-warning">ADD</button>
          </div>
        </div>
        <div class="form-group mt-3">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="e-invisible" id="e-invisible">
            <label class="form-check-label" for="e-invisible">Invisible to public</label>
          </div>
        </div>
        
        <button type="submit" class="btn btn-success mt-3" name="submit-e">
          Submit
        </button>
      </form>
    </div>
  </div>

  <div class="form-group mt-3">
    <button
      class="btn btn-secondary mb-3"
      onclick="location.href='{{ url_for('admin') }}';"
    >
      Back
    </button>
  </div>
</div>

<script>

  $('#a-unit').on('change',() => {
    $('#a-unit').val($('#a-unit').val().toUpperCase())
  })

  const tabs = document.querySelectorAll('.nav-link');

  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');

      switch (tab.getAttribute('id')) {
        case 'tab-a':
          var activeForm = document.getElementById('add-unit');
          break;
        case 'tab-e':
          var activeForm = document.getElementById('edit-unit');
          break;
      }

      const forms = document.querySelectorAll('.tab-pane');
      forms.forEach(f => f.classList.remove('show', 'active'));
      activeForm.classList.add('show','active');

    });

  });


  // function to change the unit dropdown list
  function updateUnitDropdown(ele_d,ele_u) {

    const departmentSelect = document.getElementById(ele_d);
    const unitSelect = document.getElementById(ele_u);

    const selectedDepartment = departmentSelect.value;
    unitSelect.innerHTML = '';

    const unitsByDepartment = {{ units_by_department|safe }}
    const units = unitsByDepartment[selectedDepartment] || [];
    units.sort((a, b) => {
      if (a === "ALL") return -1; // Move 'ALL' to the beginning
      if (b === "ALL") return 1; // Move 'ALL' to the beginning
      return 0; // Keep the original order for other items
    });
    for (const u of units) {
      const option = document.createElement('option');
      option.value = u;
      option.text = u;
      unitSelect.appendChild(option);
    };
  }

  // Unit dropdown change dynamically according to the selected department
  [['e-department','e-unit']].forEach(ele => {
    const departmentSelect = document.getElementById(ele[0]);
    departmentSelect.addEventListener('change', () => {updateUnitDropdown(ele[0], ele[1]);});

    // Trigger the change event to populate the email dropdown on page load
    departmentSelect.dispatchEvent(new Event('change'));
  })

  // When [ ADD NEW ] is selected, input field for Department is enabled, and vice versa
  $(document).ready(function() {
    $('#a-department').on('change', function() {

      // Find the 'other' input within this div
      var otherInput = $('#dept_other');

      // Check if any of the selected options in this group is "Other:"
      var isOtherSelected = $('#a-department').val() === '[ ADD NEW ]';

      // If the 'other' option is selected, enable the input field and make it required
      if (isOtherSelected) {
          otherInput.prop('required', true);
          otherInput.prop('disabled', false);
      } 
      // Otherwise, disable the input field and make it not required
      else {
          otherInput.prop('required', false);
          otherInput.val('');  // clear the input field
          otherInput.prop('disabled', true);
        }
    });
  });

  // In Acess field, when ADD button beside the select list is clicked, 
  // selected value is appended to the input field
  [
    ['addAccessButton','add-access-department','add-access'],
    ['editAccessButton','edit-access-department','edit-access']
  ].forEach(
    ele => {
      document.getElementById(ele[0]).addEventListener('click', function() {
        var selectedDepartment = document.getElementById(ele[1]).value;
        var inputField = document.getElementById(ele[2]);
        if (!inputField.value.trim()) {
          inputField.value += selectedDepartment;
        } else {
          inputField.value += `, ${selectedDepartment}`;
        }
      });

    }
  )


  // When new department is input, an option is updated in the Access select list
  document.getElementById('dept_other').addEventListener('change', function() {

    this.value = this.value.toUpperCase();
    const departmentSelect = document.getElementById('add-access-department');
    let otherOption = departmentSelect.querySelector('option[id="new_dept"]');
    
    if (!otherOption) {
      // Create a new option element
      const newOption = document.createElement('option');
      newOption.id = "new_dept"
      newOption.value = this.value;
      departmentSelect.insertBefore(newOption, departmentSelect.firstChild);
      
      otherOption = newOption;
    }
    
    // Update the value of the option element
    otherOption.textContent = this.value;
    otherOption.selected = true;
  });


</script>
{% endblock %}
