{% extends 'base.html' %} 

{% block content %}
<style>
  .card-header {
    background-color: #c9e1bf; /* Replace with the desired background color */
  }

  .question-text {
    color: #1e159d;
  }
</style>

<h1>Fall Prevention Quiz</h1>
<div class="container">
  <form id="complete-form" action="" method="post">
    <div class="card mt-5">
      <div class="card-header">
        <h3>Staff Info</h3>
      </div>
      <div class="card-body">
        <div class="form-group mt-3">
          <label for="department">Department:</label>
          <select class="form-select" name="department" id="department">
            {% for dept in departments %}
            <option value="{{ dept }}">{{ dept }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="unit">Unit:</label>
          <select class="form-select" name="unit" id="unit">
            <!-- Unit options will be populated using JavaScript -->
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="name">Name:</label>
          <input
            class="form-control"
            type="text"
            name="name"
            id="name"
            required
          />
        </div>
        <div class="form-group mt-3">
          <label for="corpid">Employee No:</label>
          <input
            class="form-control"
            type="number"
            name="employeeno"
            id="employeeno"
            required
          />
        </div>
      </div>
    </div>

    {% for part,page in fall_test_form.items() %}
    {% if page %}
    <div class="card mt-5">
      <div class="card-header">
        <h3>{{ page.header }}</h3>
      </div>
      <div class="card-body">
        <div class="form-group">
          {% if part == 'intro' %}
          <label 
            class="mt-3 mb-3" 
          >{{ page.details|safe }}</label>
          {% else %}
          {% for id,item in page.get('details',{}).items() %}
          <label 
            class="question-text mt-3 mb-3" 
            for="{{ id }}"
          ><strong>{{ id|replace('q','')|int }} ) <br />{{ item.q|safe }}</strong></label>
          {% for option in item.opt %}
          <div class="form-check">
            <input
              class="form-check-input"
              type="radio"
              name="{{ id }}"
              id="{{ id }}_{{ loop.index }}"
              value="{{ option }}"
              required
            />
            <label
              class="form-check-label"
              for="{{ id }}_{{ loop.index }}"
            >
              {{ option }}
            </label>
          </div>
          {% endfor %} 
          
          {% if not loop.last %}
          <!-- Add the horizontal line only if it's not the last question -->
          <hr />
          {% endif %}

          {% endfor %}
          {% endif %}
        </div>
      </div>
    </div>
    {% endif %}
    {% endfor %}
    <button type="submit" class="btn btn-success mt-5 mb-5">Submit</button>
  </form>
</div>

<script src="{{ url_for('static', filename='js/utils.js') }}"></script>
<script>

dynamicUnitDropdown('department','unit',{{ units_by_department|safe }})

</script>

{% endblock %}
