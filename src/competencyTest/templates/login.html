{% extends 'base.html' %} {% block content %}
<div class="container mt-5">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-success text-white">
          <h2 class="mb-0">Login</h2>
        </div>
        <div class="card-body">
          <form action="{{ url_for('login') }}" method="POST">
            <div class="mb-3">
              <label for="email" class="form-label">Email address</label>
              <input
                type="email"
                class="form-control"
                id="email"
                name="email"
                required
              />
            </div>
            <div class="mb-3">
              <label for="password" class="form-label">Password</label>
              <input
                type="password"
                class="form-control"
                id="password"
                name="password"
                required
              />
            </div>
            <button type="submit" class="btn btn-success">Login</button>
          </form>
        </div>
      </div>
    </div>
  </div>
  <div class="row justify-content-center mt-5">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-warning text-black">
          <h2 class="mb-0">Quiz</h2>
        </div>
        <div class="card-body">
          <a 
            href="{{ url_for('fall_quiz_form') }}" 
            class="btn btn-warning"
          >Fall Quiz (Nurse)
          </a>
        </div>
        <div class="card-body">
          <a 
            href="{{ url_for('pca_fall_quiz_form') }}" 
            class="btn btn-warning"
          >Fall Quiz (PCA)
          </a>
        </div>
      </div>
    </div>
  </div>

</div>
{% endblock %}
