{% extends 'base.html' %} 


{% block content %}
<style>
  .card-header {
    background-color: #c9e1bf; /* Replace with the desired background color */
  }

  .question-text {
    color: #1e159d;
  }

  table,textarea {
    width: 100%;
  }

  textarea {
    white-space: pre-wrap;
  }

  #item-col {
    min-width: 400px;
    width: 60%;
  }

  .h-center {
    text-align: center;
  }

</style>

<h3 style="color: #b1b1b1;">New Checklist</h3>
<h1>{{ test_name }}</h1>
<div class="container">
  <form id="new-checklist" action="" method="post" >

    <input type="hidden" name="test-name" value="{{test_name}}" />

    <div class="card mt-5">
      <div class="card-header">
        <h2>Test Configuration</h2>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="version" class="form-label">Version:</label>
          <input class="form-control" type="text" value="v001" id="version" disabled />
          <label for="version-name" class="form-label mt-3">Version Name:</label>
          <input 
            class="form-control" 
            type="text" 
            id="version-name"
            name="version-name"
            placeholder="e.g. 2023 v1" 
            required 
          />
          {# <label for="score-per-q" class="form-label mt-3">Score per Question:</label>
          <input class="form-control" type="text" id="score-per-q" name="score-per-q" required />
          <label for="total-score" class="form-label mt-3">Total Score:</label>
          <input class="form-control" type="text" id="total-score" name="total-score" required />
          <label for="pass-score" class="form-label mt-3">Pass Score:</label>
          <input class="form-control" type="text" id="pass-score" name="pass-score" required /> #}
          <label for="passing-criteria" class="form-label mt-3">Passing Criteria:</label>
          <select 
            class="form-control" 
            type="text" 
            id="passing-criteria" 
            name="passing-criteria" 
            required
          >
            {% for criteria_id, criteria in passing_criteria.items() %}
            <option value="{{ criteria_id }}">{{ criteria.title }}</option>
            {% endfor %}
          </select>
          <label for="checklist-option-setting" class="form-label mt-3">Checklist Option Setting:</label>
          <div class="input-group" id="checklist-option-setting">
            <input type="radio" class="btn-check" name="option-setting" id="option-setting-1" value="one" autocomplete="off" checked>
            <label class="btn btn-outline-success" for="option-setting-1">Allow 1 option only</label>

            <input type="radio" class="btn-check" name="option-setting" id="option-setting-2" value="multiple" autocomplete="off">
            <label class="btn btn-outline-warning" for="option-setting-2">Allow multiple options</label>
          </div>
        </div>
      </div>
    </div>


    <div class="card mt-5">
      <div class="card-header">
        <input
          class="form-control"
          type="text"
          name="intro-header"
          id="intro-header"
          placeholder="Test Name Here"
        />
      </div>
      <div class="card-body">
        <div class="form-group">
          <textarea 
            class=" form-control mt-3 mb-3" 
            id="intro-details"
            name="intro-details"
            rows="5"
            placeholder="Introduction / Instruction Here"
            required
          ></textarea>
        </div>
      </div>
    </div>

    <div class="card mt-5">
      <div class="card-header">
        <input
          class="form-control"
          type="text"
          name="basic-entry-header"
          id="basic-entry-header"
          placeholder="Basic Entry Header Here (e.g. Basic Entry)"
        />
      </div>
      <div class="card-body">
        <div class="form-group" id="basic-entry">
          
          <button
            type="button" 
            id="new-entry"
            class="btn btn-warning mt-3 mb-3 new-basic-entry"
            onclick="addEntry(this)"
          >
            Add New Entry
          </button>
        </div>
      </div>
    </div>


    <div class="card mt-5">
      <div class="card-header">
        <input
          class="form-control"
          type="text"
          name="questions-header"
          id="questions-header"
          placeholder="Checklist Header Here (e.g. Checklist)"
        />
      </div>
      <div class="card-body">

        <div class="d-flex justify-content-center">
          <button 
            type="button" 
            class="btn btn-success mt-3 mb-3 add-col"
            data-bs-toggle="modal"
            data-bs-target="#newColumnModal"
          >
            Add New Column
          </button>
        </div>

        <div class="table-responsive">
          <table id="checklist-content" class="table table-bordered">
            <thead>
              <tr>
                <th></th>
                <th class="h-center">
                  <button 
                    type="button" 
                    class="btn btn-danger" 
                    onclick="delColumn(this)"
                  ><i class="fa-solid fa-trash" style="color: #ffffff;"></i>
                  </button>
                </th>
                <th class="h-center">
                  <button 
                    type="button" 
                    class="btn btn-danger" 
                    onclick="delColumn(this)"
                  ><i class="fa-solid fa-trash" style="color: #ffffff;"></i>
                  </button>
                </th>
              </tr>
              <tr>
                <th id="item-col">
                  <input 
                    type="text" 
                    name="col-item" 
                    class="form-control"
                    placeholder="Column Name Here (e.g. Item / Steps)" />
                </th>
                <th>
                  <input type="hidden" name="ans-1-type" id="ans-1-type" value="checkbox"/>
                  <div class="d-flex align-items-center mb-1">
                    <input type="checkbox" id="cs-1" name="cs-1" checked />
                    <label class="form-check-label ms-2" for="cs-1">Count Score</label>
                  </div>
                  <input type="text" class="form-control ans-col" name="ans-1" id="ans-1" value="Pass" />
                </th>
                <th>
                  <input type="hidden" name="ans-2-type" id="ans-2-type" value="text"/>
                  <input type="text" class="form-control ans-col" name="ans-2" id="ans-2" value="Remarks" />
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <div class="d-flex align-items-center">
                    <textarea 
                      name="item-1" 
                      id="item-1" 
                      rows="3"
                    ></textarea>
                    <div class="ms-2 border border-danger rounded">
                      <input 
                        type="checkbox"
                        class="btn-check me-2 item-essential"
                        id="item-essential-1"
                        name="item-essential-1"
                        onchange="essential__onChange(this)"
                      />
                      <label 
                        class="btn btn-danger item-essential" 
                        for="item-essential-1"
                      >Essential</label>
                    </div>
                    <button 
                      type="button" 
                      class="btn btn-danger ms-2"
                      onclick="delItem(this)">
                      <i class="fa-solid fa-trash" style="color: #ffffff;"></i>
                    </button>
                  </div>
                </td>
                <td class="h-center">
                  <input type="checkbox" class="form-check-input" />
                </td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </div>

        <div
          class="d-flex justify-content-center mb-3"
        >
          <button
            type="button" 
            class="btn btn-warning mt-3 mb-3 new-Q"
            onclick="addItem()"
          >
            Add New Item
          </button>
        </div>

      </div>
    </div>

    <div class="card mt-5">
      <div class="card-header">
        <input
          class="form-control"
          type="text"
          name="additional-entry-header"
          id="additional-entry-header"
          placeholder="Additional Entry Header Here (e.g. Additional Entry)"
        />
      </div>
      <div class="card-body">
        <div class="form-group" id="additional-entry">
          
          <button
            type="button" 
            id="new-additional-entry"
            class="btn btn-warning mt-3 mb-3 new-additional-entry"
            onclick="addEntry(this)"
          >
            Add New Entry
          </button>
        </div>
      </div>
    </div>

    <button type="button" class="btn btn-success mt-5 mb-5" onclick="saveForm()">Save</button>
  </form>
</div>

<!-- New Column Modal -->
<div 
  class="modal fade" 
  id="newColumnModal" 
  tabindex="-1" 
  aria-labelledby="newColumnModalLabel" 
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header modal-header-color">
        <h1 class="modal-title fs-5" id="newColumnModalLabel">New Column</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <select class="form-select" name="new_column" id="new_column">
          <option value="checkbox">Checkbox</option>
          <option value="text">Text</option>
        </select>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" data-bs-dismiss="modal" onclick="addColumn()">Add</button>
      </div>
    </div>
  </div>
</div>

<script src="{{ url_for('static', filename='js/new_checklist.js') }}"></script>


<script>

  $(document).ready(function () {
    $(".item-essential").trigger("change");
  })


</script>

{% endblock %}
