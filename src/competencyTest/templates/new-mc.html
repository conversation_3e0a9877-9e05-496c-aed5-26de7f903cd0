{% extends 'base.html' %} 

{% block content %}
<style>
  .card-header {
    background-color: #c9e1bf; /* Replace with the desired background color */
  }

  .question-text {
    color: #1e159d;
  }
</style>

<h3 style="color: #b1b1b1;">New MC</h3>
<h1>{{ test_name|replace("_", " ") }}</h1>
<div class="container">
  <form id="new-mc" action="" method="post">

    <input type="hidden" name="test-name" value="{{test_name}}" />

    <div class="card mt-5">
      <div class="card-header">
        <h2>{{ test_name|replace("_", " ") }}</h2>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="version" class="form-label">Version:</label>
          <input class="form-control" type="text" value="v001" id="version" disabled />
          <label for="version-name" class="form-label mt-3">Version Name:</label>
          <input 
            class="form-control" 
            type="text" 
            id="version-name"
            name="version-name"
            placeholder="e.g. 2023 v1" 
            required />
          <label for="score-per-q" class="form-label mt-3">Score per Question:</label>
          <input class="form-control" type="text" id="score-per-q" name="score-per-q" required />
          <label for="total-score" class="form-label mt-3">Total Score:</label>
          <input class="form-control" type="text" id="total-score" name="total-score" required />
          <label for="pass-score" class="form-label mt-3">Pass Score:</label>
          <input class="form-control" type="text" id="pass-score" name="pass-score" required />
        </div>
      </div>
    </div>

    
    <div class="card mt-5">
      <div class="card-header">
        <input
          class="form-control"
          type="text"
          name="intro-header"
          id="intro-header"
          placeholder="Introduction Header" 
          required
        />
      </div>
      <div class="card-body">
        <div class="form-group">
          <textarea 
            class=" form-control mt-3 mb-3" 
            id="intro-details"
            name="intro-details"
            rows="5"
            placeholder="Introduction Details Here"
            required
          ></textarea>
        </div>
      </div>
    </div>

    <div class="card mt-5">
      <div class="card-header">
        <input
          class="form-control"
          type="text"
          name="questions-header"
          id="questions-header"
          placeholder="Questions Header"
          required
        />
      </div>
      <div class="card-body">
        <div class="form-group">
          <div class="question-container">
            <div
              class="d-flex justify-content-between mb-3"  
            >
              <h3 class="mt-3">q01</h3>
              <button 
                type="button" 
                class="btn btn-danger mt-3 mb-3 del-Q"
              >
                Delete Question
              </button>
            </div>
            <textarea
              class="question-text mt-3 mb-3 form-control"
              id="q01"
              name="q01"
              rows="5"
            ></textarea>
            {% for i in range(1,5) %}
            <div class="input-group option-container mb-2">
              
              <div class="input-group-text">
              <input 
                  type="radio"
                  class="form-check-input" 
                  name="q01-ans" 
                  value="{{ i }}"
                  autocomplete="off" 
                  required
                />
              </div>
            
              <input
                class="form-control"
                type="text"
                name="q01_{{ i }}"
                id="q01_{{ i }}"
                onchange="optionText__onChange(this)"
                required
              />
              <button
                type="button"
                class="btn btn-secondary del-O"
              >
                <i class="fa-solid fa-trash" style="color: #ffffff;"></i>
              </button>
            </div>
            {% endfor %} 

            <button
              type="button" 
              class="btn btn-primary mb-3 new-O"
              data-id="q01"
            >
              Add New Option
            </button>

            
            <!-- Add the horizontal line -->
            <hr />

          </div>

          <div
            class="d-flex justify-content-center mb-3"
          >
            <button
              type="button" 
              class="btn btn-warning mt-3 mb-3 new-Q"
            >
              Add New Question
            </button>
          </div>
        </div>
      </div>
    </div>
    <button type="submit" class="btn btn-warning mt-5 mb-5 ms-2">Create</button>
  </form>
</div>

<script>

function removeQuestionContainer() {
  // Get the button element that was clicked
  const button = $(this);

  // Traverse up the DOM tree to find the ancestor div with class "question-container"
  const questionContainer = button.closest('.question-container');

  // Remove the question container from the DOM
  questionContainer.remove();
}

function removeOptionContainer() {
  // Get the button element that was clicked
  const button = $(this);

  // Traverse up the DOM tree to find the ancestor div with class "option-container"
  const optionContainer = button.closest('.option-container');

  // Remove the option container from the DOM
  optionContainer.remove();

}

function addNewOption() {

  // Get the button element that was clicked
  const button = $(this);
  const id = button.attr('data-id');

  // Traverse up the DOM tree to find the ancestor div with class "question-container"
  const questionContainer = button.closest('.question-container');

  // Get the number of existing options
  const optionCount = questionContainer.find('.option-container').length;

  // Create the new option container
  const newOptionContainer = $('<div>').addClass('input-group option-container mb-2');

  // Create the radio element to indicate the answer
  const newOptionRadio = $('<div>')
    .addClass('input-group-text')
    .html(`
      <input 
        type="radio" 
        class="form-check-input" 
        name="${id}-ans" 
        value= "${optionCount + 1}"
        autocomplete="off"
        required
      />
    `);

  // Create the input element for the option
  const newOptionInput = $('<input>')
    .addClass('form-control')
    .attr('type', 'text')
    .attr('name', `${id}_${optionCount + 1}`)
    .attr('id', `${id}_${optionCount + 1}`)
    .attr('required', true)
    .on('change', function() {
        optionText__onChange(this)
      });

  // Create the delete button for the option
  const deleteButton = $('<button>')
    .addClass('btn btn-secondary mb-2 del-O')
    .attr('type', 'button')
    .html('<i class="fa-solid fa-trash" style="color: #ffffff;"></i>')
    .on('click', removeOptionContainer);

  // Append the input and delete button to the new option container
  newOptionContainer.append(newOptionRadio, newOptionInput, deleteButton);

  // Insert the new option container after the last option
  const lastOptionContainer = questionContainer.find('.option-container').last();
  newOptionContainer.insertAfter(lastOptionContainer);
}

function addNewQuestion() {
  // Get the last question container
  const lastQuestionContainer = $('.question-container').last();

  // Get the ID for the new question
  const lastQuestionId = lastQuestionContainer.find('h3').text();

  // Extract the number from the ID and increment it by 1
  const newQuestionNumber = parseInt(lastQuestionId.slice(1)) + 1;
  const newQuestionId = 'q' + newQuestionNumber.toString().padStart(2, '0')

  // Create the new question container
  const newQuestionContainer = $('<div>').addClass('question-container');

  // Create the heading for the new question
  const newQuestionHeading = $('<div>').addClass('d-flex justify-content-between mb-3');
  const newQuestionHeadingText = $('<h3>').addClass('mt-3').text(newQuestionId);
  const newQuestionDeleteButton = $('<button>')
    .addClass('btn btn-danger mt-3 mb-3 del-Q')
    .attr('type', 'button')
    .text('Delete Question')
    .on('click', removeQuestionContainer);

  // Append the heading and delete button to the new question container
  newQuestionHeading.append(newQuestionHeadingText, newQuestionDeleteButton);
  newQuestionContainer.append(newQuestionHeading);

  // Create the textarea for the new question
  const newQuestionTextArea = $('<textarea>')
    .addClass('question-text mt-3 mb-3 form-control')
    .attr('id', newQuestionId)
    .attr('name', newQuestionId)
    .attr('rows', 5);

  // Append the textarea to the new question container
  newQuestionContainer.append(newQuestionTextArea);

  // Create the default options for the new question
  for (let i = 0; i < 4; i++) {
    const newOptionContainer = $('<div>').addClass('input-group option-container mb-2');
    const newOptionRadio = $('<div>')
      .addClass('input-group-text')
      .html(`
        <input 
          type="radio" 
          class="form-check-input" 
          name="${newQuestionId}-ans" 
          value= "${i + 1}"
          autocomplete="off"
          required
        />
      `);    
    const newOptionInput = $('<input>')
      .addClass('form-control')
      .attr('type', 'text')
      .attr('name', `${newQuestionId}_${i + 1}`)
      .attr('id', `${newQuestionId}_${i + 1}`)
      .attr('required', true)
      .on('change', function() {
        optionText__onChange(this)
      });
    const newOptionDeleteButton = $('<button>')
      .addClass('btn btn-secondary del-O')
      .attr('type', 'button')
      .html('<i class="fa-solid fa-trash" style="color: #ffffff;"></i>')
      .on('click', removeOptionContainer);
    newOptionContainer.append(newOptionRadio ,newOptionInput, newOptionDeleteButton);
    newQuestionContainer.append(newOptionContainer);
  }

  // Create the "Add New Option" button for the new question
  var newOptionButton = $('<button>')
    .addClass('btn btn-primary mb-3 new-O')
    .attr('data-id', newQuestionId)
    .attr('type', 'button')
    .text('Add New Option')
    .on('click', addNewOption);

  // Append the "Add New Option" button to the new question container
  newQuestionContainer.append(newOptionButton);

  newQuestionContainer.append($('<hr />'));

  // Insert the new question container after the last question container
  newQuestionContainer.insertAfter(lastQuestionContainer);

}

function optionText__onChange(input) {
  const optionRadio = input.previousElementSibling.querySelector('[name$="-ans"]')
  optionRadio.value = input.value
  console.log(input.value)
}

function submitNewForm() {
  document.getElementById('new-mc').submit();
}

$(document).ready(function() {
  // Attach the click event handler to the "Delete Question" button
  $('.del-Q').on('click', removeQuestionContainer);

  // Attach the click event handler to the "Delete Option" button
  $('.del-O').on('click', removeOptionContainer);

  // Attach the click event handler to the "Add New Option" button
  $('.new-O').on('click', addNewOption);

  // Attach the click event handler to the "Add New Question" button
  $('.new-Q').on('click', addNewQuestion);


  // Attach the click event handler to the "Save As New" button
  $('#saveAsNew').on('click', submitNewForm);

});
  
</script>

{% endblock %}
