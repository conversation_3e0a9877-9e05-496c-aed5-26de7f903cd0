{% extends 'base.html' %} 

{% block content %}
<style>
  .card-header {
    background-color: #c9e1bf; /* Replace with the desired background color */
  }

  .question-text {
    color: #1e159d;
  }

  table,textarea {
    width: 100%;
  }

  .item-content,label {
    white-space: pre-wrap;
  }

  #item-col {
    min-width: 400px;
    width: 60%;
  }

  .h-center {
    text-align: center;
  }

  .v-center {
    vertical-align: middle;
  }

  .bold-large {
    font-weight: bold;
    font-size: large;
  }

</style>

<h1>PCA Checklists</h1>

<div class="container">
  <form id="complete-form" action="" method="post">

    {# <input type="hidden" name="test_name" value="{{ test_name }}">
    <input type="hidden" name="test_version" value="{{ checklist_info.version }}"> #}

    <div class="card mt-5">
      <div class="card-header">
        <h3>指導員</h3>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="tutor_name">Name:</label>
          <input
            class="form-control org-names"
            type="text"
            name="tutor_name"
            id="tutor_name"
            value="{{ tutor_name }}"
            required
          />
        </div>
        <div class="form-group mt-2">
          <label for="tutor_rank">Rank:</label>
          <select class="form-select org-ranks" name="tutor_rank" id="tutor_rank">
            {% for rank in ranks %}
            {% if tutor_rank == rank %}
            <option value="{{ rank }}" selected>{{ rank }}</option>
            {% else %}
            <option value="{{ rank }}">{{ rank }}</option>
            {% endif %}
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-2">
          <label for="tutor-date">Date:</label>
          <input class="form-control org-dates" type="date" name="tutor_date" id="tutor-date" required />
        </div>
      </div>
      <div class="card-footer">
        <button type="button" class="btn btn-warning" id="apply-to-all">Apply to all</button>
      </div>
    </div>

    <div class="card mt-5">
      <div class="card-header">
        <h3>觀察員</h3>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="observer_name">Name:</label>
          <input
            class="form-control org-names"
            type="text"
            name="observer_name"
            id="observer_name"
            value="{{ observer_name }}"
            required
          />
        </div>
        <div class="form-group mt-2">
          <label for="observer_rank">Rank:</label>
          <select class="form-select org-ranks" name="observer_rank" id="observer_rank">
            {% for rank in ranks %}
            {% if observer_rank == rank %}
            <option value="{{ rank }}" selected>{{ rank }}</option>
            {% else %}
            <option value="{{ rank }}">{{ rank }}</option>
            {% endif %}
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-2">
          <label for="observer-date">Date:</label>
          <input class="form-control org-dates" type="date" name="observer_date" id="observer-date" required />
        </div>
      </div>
    </div>

    <div class="card mt-5">
      <div class="card-header">
        <h3>評核員</h3>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="assessor_name">Name:</label>
          <input
            class="form-control org-names"
            type="text"
            name="assessor_name"
            id="assessor_name"
            value="{{ assessor_name }}"
            required
          />
        </div>
        <div class="form-group mt-2">
          <label for="assessor_rank">Rank:</label>
          <select class="form-select org-ranks" name="assessor_rank" id="assessor_rank">
            {% for rank in ranks %}
            {% if assessor_rank == rank %}
            <option value="{{ rank }}" selected>{{ rank }}</option>
            {% else %}
            <option value="{{ rank }}">{{ rank }}</option>
            {% endif %}
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-2">
          <label for="assessor-date">Date:</label>
          <input class="form-control org-dates" type="date" name="assessor_date" id="assessor-date" required />
        </div>
      </div>
    </div>

    <div class="card mt-5">
      <div class="card-header">
        <h3>Staff Info</h3>
      </div>
      <div class="card-body">
        <div class="form-group mt-3">
          <label for="pca_dept">Department:</label>
          <select class="form-select" name="pca_dept" id="pca_dept">
            {% for dept in allowed_departments %}
              {% if pca_dept == dept %}
                <option value="{{ dept }}" selected>{{ dept }}</option>
              {% else %}
                <option value="{{ dept }}">{{ dept }}</option>
              {% endif %}
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="unit">Unit:</label>
          <select class="form-select" name="pca_unit" id="pca_unit">
            <!-- Unit options will be populated using JavaScript -->
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="pca_name">Name:</label>
          <input
            class="form-control"
            type="text"
            name="pca_name"
            id="pca_name"
            required
          />
        </div>
        <div class="form-group mt-3">
          <label for="pca_employeeno">Employee No:</label>
          <div class="form-control input-group">
            <input
              class="form-control"
              type="number"
              name="pca_employeeno"
              id="pca_employeeno"
              required
            />
            <input 
              class="btn-check" 
              type="checkbox"
              name="old_temp_staff"
              id="old_temp_staff"
              autocomplete="off"
            />
            <label 
              class="btn btn-outline-success" 
              for="old_temp_staff"
            >Existing TEMP</label>
          </div>
          {# checkbox for temp staff #}
          <input type="hidden" name="new_temp_staff_no" id="new_temp_staff_no"/>
          <div class="form-check mt-1">
            <input
              class="form-check-input"
              type="checkbox"
              name="new_temp_staff"
              id="new_temp_staff"
            />
            <label class="form-check-label" for="temp_staff"
            ><strong style="color:red;">NEW </strong>TEMP (for staff without employee number)</label>
          </div>
        </div>
      </div>
    </div>


    <div class="card mt-5">
      <div class="card-header">
        <div class="d-flex justify-content-between">
          <h3>Choose Assessments</h3>
          <div class="d-flex justify-content-end">
            <button 
              type="button"
              class="btn btn-primary me-2"
              id="selectAllBtn"
            >
              Select All
            </button>
            <button 
              type="button"
              class="btn btn-danger"
              id="unselectAllBtn"
            >
              Unselect All
            </button>
          </div>
        </div>

      </div>
      <div class="card-body">
        <ul class="list-group mb-2">
          {% for test in pca_tests %}
            <li class="list-group-item">
              <div class="form-check d-flex align-items-center">
                <input class="form-check-input test-option" type="checkbox" value="{{test['test_name']}}" name="checklist_{{ loop.index }}" id="checklist_{{ loop.index }}">
                <a href="{{ url_for('checklist_form', test_name=test['test_name']) }}" class="btn btn-warning ms-2" for="checkist_{{ loop.index }}">
                  {{test['test_name']}} | {{test['title']}}
                </a>
              </div>
            </li>
          {% endfor %}
        </ul>
      </div>
    </div>


    <button type="submit" id="form-submit" class="btn btn-success mt-5 mb-5">Submit</button>
  </form>
</div>

<button type="button" id="trigger-modal" class="btn btn-primary" data-toggle="modal" data-target="#confirm-pca-modal" hidden></button>
<div class="modal fade" id="confirm-pca-modal" tabindex="-1" role="dialog" aria-labelledby="confirm-pca-modal-label" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="confirm-pca-modal-label">Confirm PCA</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <h1 class="mb-3" id="found-pca">Found Existing PCA</h1>
        <div class="mb-3">
          <label for="modal-pca-employeeno" class="form-label">Employee No.:</label>
          <input type="text" class="form-control" id="modal-pca-employeeno" value="" readonly>
        </div>
        <div class="mb-3">
          <label for="modal-pca-name" class="form-label">Name:</label>
          <input type="text" class="form-control" id="modal-pca-name" value="" readonly>
        </div>
        <div class="mb-3">
          <label for="modal-pca-dept" class="form-label">Department:</label>
          <input type="text" class="form-control" id="modal-pca-dept" value="" readonly>
        </div>
        <div class="mb-3">
          <label for="modal-pca-unit" class="form-label">Unit:</label>
          <input type="text" class="form-control" id="modal-pca-unit" value="" readonly>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="confirm-pca-btn">Confirm</button>
      </div>
    </div>
  </div>
</div>

<div
  id="data-container"
  data-pcas='{{ pcas | tojson | safe }}'
  data-temp-staff='{{ temp_staff | tojson | safe }}'
  data-units-by-department='{{ units_by_department | tojson | safe }}'
  data-next-temp-idx='{{ next_temp_idx | safe }}'
></div>



<script type="module" src="{{ url_for('static', filename='js/pca_checklists.js') }}"></script>


{% endblock %}
