{% extends 'base.html' %} 

{% block content %}

<style>
  body {
    align-items: center;
    justify-content: center;
    height: 100vh;
    margin: 0;
  }

  .score-circle-container {
    text-align: center;
  }

  .score-circle {
    width: 80vmin;
    height: 80vmin;
    border-radius: 50%;
    background-color: {{ 'green' if quiz_pass=='True' else 'red' }};
    color: white;
    font-size: 6vw;
    font-weight: bold;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s, transform 0.3s;
    cursor: pointer;
    margin: 0 auto;
    margin-bottom: 20px;
  }

  .score-circle:hover {
    transform: translateY(-5%);
  }

  .score-text {
    margin-bottom: 8px;
  }

  .pass-text {
    font-size: 4vw;
    font-weight: bold;
  }

  .test-again-button {
    padding: 12px 24px;
    background-color: yellow;
    color: black;
    border: none;
    border-radius: 20px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    transition: transform 0.3s;
  }

  .test-again-button:hover {
    transform: scale(1.05);
  }

  @media (max-width: 600px) {
    .score-circle {
      font-size: 10vw;
    }
  }
</style>

<div class="score-circle-container">
  <div class="score-circle">
    <div class="score-text">{{ score }}</div>
    <div class="pass-text">{{ 'Pass' if quiz_pass=='True' else 'Fail' }}</div>
  </div>
  <button
    class="test-again-button"
    onclick="window.location.href='{{ url_for(re_test_name) }}'"
  >
    Test Again
  </button>
</div>

{% endblock %}
