{% extends 'base.html' %} {% block custom_head %}
<script src="https://cdn.jsdelivr.net/npm/ag-grid-community/dist/ag-grid-community.min.js"></script>
<link
  href="https://fonts.googleapis.com/css?family=Roboto:wght@800&display=swap"
  rel="stylesheet"
/>
<style>
  html,
  body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }

  .bg-primary {
    background-color: #007bff !important;
  }

  tr.even {
    background-color: #f2f2f2;
  }

  .d-flex {
    display: flex;
  }

  .justify-content-between {
    justify-content: space-between;
  }

  .align-items-center {
    align-items: center;
  }

  .form-group {
    margin-bottom: 0;
  }

  #grid-container {
    width: 100%;
    height: 100%;
  }
  #grid {
    width: 100%;
    height: 80%;
  }

  .container {
    width: 100%;
    height: 100%;
  }

  .tab-content {
    width: 100%;
    height: 100%;
  }

  .tab-pane {
    width: 100%;
    height: 100%;
  }

  table.custom-table thead th {
    background-color: #0a8853;
    font-family: "Roboto", sans-serif;
    font-weight: 800;
    color: white;
  }

  .btn-link {
    text-decoration: none !important;
  }
</style>
{% endblock %} 

{% block content %}
<div class="container">
  <div class="d-flex justify-content-between align-items-center">
    <div>
    <h1>{{view_result}}</h1>
    {% if query_by == 'person' %}
    <h2><b style="color: blue;">{{ result_data[0].get('Name', 'NIL') }} ({{ result_data[0].get('Employee_No','NIL') }})</b></h2>
    {% endif %}
    </div>
    <div class="form-group">
      <button
        id="back-button"
        class="btn btn-secondary"
      >
        Back
      </button>
    </div>
  </div>
  {% if query_test != 'ALL'%}
    <h1><strong>{{query_test|replace('_', ' ')}}</strong></h1>
    <h2><strong>{{test_form.content.get('intro',{}).get('header','')}}</strong></h2>
    <div class="mb-3">
      <small class="text-muted mb-3">{{test_form.info.version_name}}</small>
    </div>
  {% endif %}
  <!--
  <div class="d-flex justify-content-between align-items-center">
    <h3>Department: {{ dept }}</h3>
    <div class="form-group">
      <button
        class="btn btn-success float-end mb-3"
        onclick="exportTableToExcel()"
      >
        Export
      </button>
    </div>
  </div>
  -->

  <!-- Nav tabs -->
  <ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
      <a
        class="nav-link active"
        data-toggle="tab"
        href="#result-ag"
        id="tab-ag"
        role="tab"
        >Interactive</a
      >
    </li>
    <li class="nav-item">
      <a
        class="nav-link"
        data-toggle="tab"
        href="#result-tr"
        id="tab-tr"
        role="tab"
        >Plain</a
      >
    </li>
  </ul>

  <div class="tab-content mt-3">
    <!-- AG Grid -->
    <div class="tab-pane fade show active" id="result-ag" role="tabpanel">
      <div id="grid-container">
        <div id="grid" class="ag-theme-alpine"></div>
      </div>
    </div>

    <!-- Plain Table -->
    <div class="tab-pane fade" id="result-tr" role="tabpanel">
      <table
        class="table table-striped table-bordered table-hover custom-table"
      >
        <thead>
          <tr>
            {% if query_test != 'ALL'%}
              <th>Unit</th>
              <th>Name</th>
              <th>Employee_No</th>
            {% else %}
              <th>Test</th>
            {% endif %}
            <th>Result</th>
          </tr>
        </thead>
        <tbody>
          {% if query_test != 'ALL' %}
            {% for row in result_data %}
              <tr>
                {% for k,v in row.items() %}
                  {% if k == 'Result' and v != "[ NIL ]" %}
                    <td>
                      <button
                        id="plain-{{ row.Employee_No }}"
                        class="btn btn-link p-0 plain-table-test-btn"
                        data-name="{{ row.Name }} ({{ row.Employee_No }})"
                        data-employee-no="{{ row.Employee_No }}"
                        data-staff-test='{{ v| tojson | safe }}'
                        data-test-form='{{ test_form | tojson | safe }}'
                      >
                        {% if v['pass'] %}
                          <span 
                            style="color: green; text-decoration: none"
                          ><strong>Pass on {{v['time'].split(' ')[0]}}</strong>
                          </span>
                        {% else %}
                          <span 
                            style="color: red; text-decoration: none"
                          ><strong>FAIL on {{v['time'].split(' ')[0]}}</strong>
                          </span>
                        {% endif %}
                      </button>
                    </td>
                  {% elif k == 'Result' and v == "[ NIL ]" %}
                    <td>
                      <button
                        id="plain-{{ row.Employee_No}}"
                        class="btn btn-link p-0 plain-table-test-btn"
                        data-name="{{ row.Name }} ({{ row.Employee_No }})"
                        data-employee-no="{{ row.Employee_No }}"
                        data-staff-test='[ NIL ]'
                        data-test-form='{}'
                      >
                        [ NIL ]
                      </button>
                    </td>
                  {% else %}
                    <td>{{ v }}</td>
                  {% endif %}
                {% endfor %}
              </tr>
            {% endfor %}


          {% else %}
          {# if query_test == 'ALL' #}

            {% for row in result_data %}
              {% if row['Result'] == '[ NIL ]' %}
              
                <tr>
                  <td>[ NIL ]</td>
                  <td>[ NIL ]</td>
                </tr>

              {% else %}
              {# if row['Result'] != '[ NIL ]' #}

                {% for result in row['Result'] %}
                  <tr>
                  {% for k,v in row.items() %}
                    {% if k == 'Result' and v != "[ NIL ]" %}
                      <td>
                        <span>{{ result['test_name'] | replace('_', ' ') }} | {{ result['test_title'] }}</span>
                      </td>
                      <td>
                        <button
                          id="plain-{{ row.Employee_No }}-{{ loop.index }}"
                          class="btn btn-link p-0 plain-table-test-btn"
                          data-name="{{ row.Name }} ({{ row.Employee_No }})"
                          data-employee-no="{{ row.Employee_No }}"
                          data-staff-test='{{ result | tojson | safe }}'
                          data-test-form='{{ test_form[result["test_name"]] | tojson | safe }}'
                        >
                          {% if result['pass'] %}
                          <span 
                            style="color: green; text-decoration: none"
                          ><strong>Pass on {{result['time'].split(' ')[0]}}</strong>
                          </span>
                          {% else %}
                          <span 
                            style="color: red; text-decoration: none"
                          ><strong>FAIL on {{result['time'].split(' ')[0]}}</strong>
                          </span>
                          {% endif %}
                        </button>
                      </td>
                    {% elif k == 'Result' and v == "[ NIL ]" %}
                      <td>
                        <span>[ NIL ]</span>
                      </td>
                      <td>
                        <span>[ NIL ]</span>
                      </td>
                    {% endif %} 
                    
                  {% endfor %}
                  {# end for k,v in row.items() #}

                  </tr>
                {% endfor %}
                {# end for result in row['Result'] #}

              {% endif %}
              {# end if row['Result'] == '[ NIL ]' #}

            {% endfor %}
            {# end for row in result_data #}

          {% endif %}
          {# end if query_test != 'ALL' #}

        </tbody>
      </table>
    </div>
  </div>


  {% if query_test != 'ALL' %}
    {% if test_form.info.form_type == 'mc' %}
      <!-- MC Result Modal -->
      <div
        class="modal fade"
        id="mc-result"
        tabindex="-1"
        aria-labelledby="mc-result-title"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-fullscreen">
          <div class="modal-content">
            <div class="modal-header" style="background-color: #26a682">
              <h5 class="modal-title" id="mc-result-title" style="color: white">
                {{test_form.info.test_name}}
              </h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body" id="mc-content">
              <form id="form-reply">
                {% for part,page in test_form.content.items() %}
                <div class="card mt-5">
                  <div class="card-header">
                    <h3>{{ page.header }}</h3>
                  </div>
                  <div class="card-body">
                    <div class="form-group">
                      {% if part == 'intro' %}
                      <label 
                        class="mt-3 mb-3" 
                      >{{ page.details|safe }}</label>
                      {% else %}
                      {% for id,item in page.details.items() %}
                      <label
                        class="question-text mt-3 mb-3"
                        for="{{ id }}"
                      ><strong>{{ id|replace('q','')|int }} ) <br/>{{ item.q|safe }}</strong></label>
                      
                      {% if item.opt|length > 0 %}
                      <div class="form-check" id="div-{{ id }}"></div>
                      {% else %}
                      <input
                        style="display: none"
                        type="text"
                        id="div-{{ id }}"
                        class="form-control"
                        disabled
                      />
                      {% endif %} 

                      {% if not loop.last %}
                      <!-- Add the horizontal line only if it's not the last question -->
                      <hr />
                      {% endif %}
                      
                      {% endfor %}
                      {% endif %} 
                    </div>

                  </div>
                </div>
                {% endfor %}
              </form>
            </div>
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-secondary"
                data-bs-dismiss="modal"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    {% else %}
      <!-- Checklist Result Modal -->
      <div 
        class="modal fade" 
        id="checklist-result" 
        tabindex="-1" 
        aria-labelledby="checklist-result-title" 
        aria-hidden="true"
      >
        <div class="modal-dialog modal-fullscreen">
          <div class="modal-content">
            <div class="modal-header" style="background-color: #26a682">
              <h5 class="modal-title" id="checklist-result-title" style="color: white"></h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="checklist-content">
              <!-- Content will be dynamically injected here -->
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    {% endif %}

  {% else %}

    <!-- MC Result Modal -->
    <div
      class="modal fade"
      id="mc-result"
      tabindex="-1"
      aria-labelledby="mc-result-title"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
          <div class="modal-header" style="background-color: #26a682">
            <h5 class="modal-title" id="mc-result-title" style="color: white"></h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body" id="mc-content">
            <!-- Content will be dynamically injected here -->
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>


    <!-- Checklist Result Modal -->
    <div 
      class="modal fade" 
      id="checklist-result" 
      tabindex="-1" 
      aria-labelledby="checklist-result-title" 
      aria-hidden="true"
    >
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
          <div class="modal-header" style="background-color: #26a682">
            <h5 class="modal-title" id="checklist-result-title" style="color: white"></h5>
            <button 
              type="button" 
              class="btn-close" 
              data-bs-dismiss="modal" 
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body" id="checklist-content">
            <!-- Content will be dynamically injected here -->
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>



  {% endif %}

  <!-- Confirm Redirect Modal -->
  <div
    class="modal fade"
    id="confirm-redirect"
    tabindex="-1"
    aria-labelledby="confirm-redirect-title"
    aria-hidden="true"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="confirm-redirect-title">Confirmation</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body">
          <p id="fill-in-assessment">
            Fill in assessment form for this operator?
          </p>
          <!-- Hidden form -->
          <form id="redirect-form" method="POST" action="">
            <input type="hidden" id="redirect-employeeNo" name="redirect-employeeNo" />
            <input type="hidden" id="redirect-test" name="redirect-test" />
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary" id="confirm-rdr-btn">
            Confirm
          </button>
        </div>
      </div>
    </div>
  </div>
</div>


<div id="data-container"
  data-query-by="{{ query_by }}"
  data-query-test="{{ query_test }}"
  data-result-data='{{ result_data | tojson | safe }}'
  data-test-form='{{ test_form | tojson | safe }}'
  data-url-for-view="{{ url_for('view') }}"
></div>

<script type="module" src="{{ url_for('static', filename='js/result.js') }}"></script>

{% endblock %}
