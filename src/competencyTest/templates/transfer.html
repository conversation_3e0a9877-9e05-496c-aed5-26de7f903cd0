{% extends 'base.html' %} {% block custom_head %} {% endblock %} {% block
bodyTop %}
<div class="container">
  <div
    class="alert alert-dismissible fade show"
    role="alert"
    id="flash-message-transfer"
    style="display: none"
  ></div>
</div>
{% endblock %} {% block content %}
<div class="container mt-4">
  <form id="transfer-form" method="POST">
    <div class="form-group mt-3">
      <label for="corp_id">CORP ID:</label>
      <input
        type="text"
        name="corp_id"
        class="form-control"
        id="corp_id"
        required
      />
    </div>
    <div class="form-group mt-3">
      <label for="department">Department to Transfer to:</label>
      <select class="form-select" name="department" id="department">
        {% for department in departments %}
        <option value="{{ department }}">{{ department }}</option>
        {% endfor %}
      </select>
    </div>
    <div class="form-group mt-3">
      <label for="unit">Unit to Tansfer to:</label>
      <select class="form-select" name="unit" id="unit"></select>
    </div>
    <button type="button" class="btn btn-primary mt-3" id="submit-btn">
      Submit
    </button>
    <button
      type="button"
      class="btn btn-secondary mt-3 ms-3"
      onclick="location.href='{{ url_for('ui') }}';"
    >
      Back
    </button>
  </form>
</div>

<button
  type="button"
  id="trigger-modal"
  class="btn btn-primary"
  data-toggle="modal"
  data-target="#confirm-transfer-modal"
  hidden
></button>
<div
  class="modal fade"
  id="confirm-transfer-modal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="confirm-transfer-modal-label"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="confirm-transfer-modal-label">
          Confirm Transfer
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <form id="transfer-form-dialog" method="POST">
          <div class="mb-3">
            <label for="input-corp-id" class="form-label">CORP ID:</label>
            <input
              type="text"
              class="form-control"
              id="input-corp-id"
              name="corp_id"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <label for="input-corp-id" class="form-label">Last Name:</label>
            <input
              type="text"
              class="form-control"
              id="last-name"
              name="last_name"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <label for="input-corp-id" class="form-label">First Name:</label>
            <input
              type="text"
              class="form-control"
              id="first-name"
              name="first_name"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <label for="input-department" class="form-label"
              >To Department:</label
            >
            <input
              type="text"
              class="form-control"
              id="input-department"
              name="department"
              value=""
              readonly
            />
          </div>
          <div class="mb-3">
            <label for="input-unit" class="form-label">To Unit:</label>
            <input
              type="text"
              class="form-control"
              id="input-unit"
              name="unit"
              value=""
              readonly
            />
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="confirm-transfer-btn">
          Confirm
        </button>
      </div>
    </div>
  </div>
</div>

<script>
    const departmentSelect = document.getElementById('department');
    const unitSelect = document.getElementById('unit');

    function updateUnitDropdown() {
        const selectedDepartment = departmentSelect.value;
        unitSelect.innerHTML = '';

        var unitsByDepartment = {{ units_by_department|safe }}
        const units = unitsByDepartment[selectedDepartment] || [];
        for (const u of units) {
        const option = document.createElement('option');
        option.value = u;
        option.text = u;
        unitSelect.appendChild(option);
        }
    }

  departmentSelect.addEventListener('change', updateUnitDropdown);

  // Trigger the change event to populate the email dropdown on page load
  departmentSelect.dispatchEvent(new Event('change'));


  document.addEventListener('DOMContentLoaded', () => {
    const form = document.querySelector('#transfer-form');
    const confirmBtn = document.querySelector('#confirm-transfer-btn');

    confirmBtn.addEventListener('click', () => {
      // Submit the form when the user confirms
      form.submit();
    });

    const submitBtn = document.querySelector('#submit-btn');
    submitBtn.addEventListener('click', (e) => {

      e.preventDefault();

      // Get user input
      const corpId = document.querySelector('#corp_id').value;
      const department = document.querySelector('#department').value;
      const unit = document.querySelector('#unit').value;

      // Get Last Name and First Name
      const operatorName = {{ operator_name|tojson|safe }};
      console.log(operatorName)
      const foundOp = Object.entries(operatorName).find(([key, value]) => key === corpId);
      var lastName = ''
      var firstName = ''
      var idFound = false

      if (foundOp) {
        lastName = foundOp[1]['Last_Name']
        firstName = foundOp[1]['First_Name']
        idFound = true
        console.log('CORP ID found.');
      } else {
        flash('flash-message-transfer','CORP ID not found','danger');
      };

      if (!idFound) { return; };

      console.log('Copying values...');

      // Populate the form with user input
      document.querySelector('#input-corp-id').value = corpId;
      document.querySelector('#last-name').value = lastName;
      document.querySelector('#first-name').value = firstName;
      document.querySelector('#input-department').value = department;
      document.querySelector('#input-unit').value = unit;

      console.log('Next Step: Load Modal...');

      // Display the confirmation dialog
      const modal = new bootstrap.Modal(document.querySelector('#confirm-transfer-modal'));
      modal.show();

    });

  });
</script>

{% endblock %}
