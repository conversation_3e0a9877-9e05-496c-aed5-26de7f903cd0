{% extends 'base.html' %} 

{% block custom_head %}
<style>
  .pale-orange {
    background-color: #ffd8b8;
  }

  .pale-yellow {
    background-color: #fafab8;
  }
</style>
{% endblock %}

{% block content %}

<div class="container mt-5">
  <h1 class="mb-5">Dashboard</h1>
  <p class="lead">Welcome, {{ user_name }}</p>
  <div class="row">
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">View</h5>
          <p class="card-text">View Staff</p>
        </div>
        <div class="card-footer">
          <a href="{{ url_for('view') }}" class="btn btn-success">Go to View</a>
        </div>
      </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">Account Management</h5>
          <p class="card-text">Change your account password.</p>
        </div>
        <div class="card-footer">
          <a href="{{ url_for('change_pw') }}" class="btn btn-success"
            >Change Password</a
          >
        </div>
      </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">Add Staff</h5>
          <p class="card-text">Register new staff profile</p>
        </div>
        <div class="card-footer">
          <a href="{{ url_for('add_staff') }}" class="btn btn-success"
            >Go to Add</a
          >
        </div>
      </div>
    </div>

  </div>

  <hr/>

  <div class="row">

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body pale-orange">
          <h5 class="card-title">Fall Quiz (Nurse)</h5>
          <p class="card-text">Do Quiz</p>
        </div>
        <div class="card-footer">
          <a 
            href="{{ url_for('fall_quiz_form') }}" 
            class="btn btn-warning"
          >Go to Quiz</a>
        </div>
      </div>
    </div>
  
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body pale-orange">
          <h5 class="card-title">AED123</h5>
          <p class="card-text">Do Checklist</p>
        </div>
        <div class="card-footer">
          <a 
            href="{{ url_for('checklist_form', test_name='AED123') }}" 
            class="btn btn-warning"
          >Go to Checklist</a>
        </div>
      </div>
    </div>


    <hr/>

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body pale-yellow">
          <h5 class="card-title">Fall Quiz (PCA)</h5>
          <p class="card-text">Do Quiz</p>
        </div>
        <div class="card-footer">
          <a 
            href="{{ url_for('pca_fall_quiz_form') }}" 
            class="btn btn-warning"
          >Go to Quiz</a>
        </div>
      </div>
    </div>



    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body pale-yellow">
          <h5 class="card-title">PCA Checklists</h5>
          <p class="card-text">Bulk Process</p>
        </div>
        <div class="card-footer">
          <a 
            href="{{ url_for('pca_checklists') }}" 
            class="btn btn-warning"
          >Go to Checklist</a>
        </div>
      </div>
    </div>


  </div>
</div>

{% endblock %}
