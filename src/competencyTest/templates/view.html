{% extends 'base.html' %} 


{% block content %}

<div class="container">
  <h1>View Data</h1>
  <!-- Nav tabs -->
  <ul class="nav nav-tabs" role="tablist">
    <li class="nav-item">
      <a
        class="nav-link {{ form_status['u'][0] }}"
        data-toggle="tab"
        href="#view-by-unit"
        id="tab-u"
        role="tab"
        >View by Unit</a
      >
    </li>
    <li class="nav-item">
      <a
        class="nav-link {{ form_status['p'][0] }}"
        data-toggle="tab"
        href="#view-by-person"
        id="tab-p"
        role="tab"
        >View by Person</a
      >
    </li>
  </ul>

  <!-- Tab panes -->
  <div class="tab-content mt-3">
    <div
      class="tab-pane fade {{ form_status['u'][1] }}"
      id="view-by-unit"
      role="tabpanel"
    >
      <form action="" method="POST" id="view-by-unit-form">
        <div class="form-group mt-3">
          <label for="test">Select Assessment:</label>
          <select name="test" id="test" class="form-select">
            {% for test_name in tests_overview %}
            <option 
              value="{{ test_name }}"
            >{{ test_name | replace('_', ' ') }} | {{ (tests_overview[test_name].values() | list)[-1]['title'][0:12] }} {{ '...' if (tests_overview[test_name].values() | list)[-1]['title'] | length > 12 else '' }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="version">Select Version:</label>
          <select name="version" id="version" class="form-select">
            <!-- version options will be populated using JavaScript -->
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="department">Select Department:</label>
          <select name="department" id="department" class="form-select">
            <option value="ALL">ALL</option>
            {% for dept in allowed_departments %}
            <option value="{{ dept }}">{{ dept }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="unit">Select Unit:</label>
          <select name="unit" id="unit" class="form-select">
            <!-- Unit options will be populated using JavaScript -->
          </select>
        </div>
        <button type="submit" class="btn btn-success mt-3" name="submit-u">
          Submit
        </button>
      </form>
    </div>

    <!-- In the view-by-person tab pane -->
    <div
      class="tab-pane fade {{ form_status['p'][1] }}"
      id="view-by-person"
      role="tabpanel"
    >
      <form action="" method="POST" id="view-by-person-form">
        <div class="form-group mt-3">
          <label for="test_person">Select Assessment:</label>
          <select name="test" id="test_person" class="form-select">
            <option value="ALL">ALL</option>
            {% for test_name in tests_overview %}
            <option 
              value="{{ test_name }}"
            >{{ test_name | replace('_', ' ') }} | {{ (tests_overview[test_name].values() | list)[-1]['title'][0:12] }} {{ '...' if (tests_overview[test_name].values() | list)[-1]['title'] | length > 12 else '' }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="version">Select Version:</label>
          <select name="version" id="version_person" class="form-select">
            <!-- version options will be populated using JavaScript -->
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="employee-no">Employee No:</label>
          <input
            type="text"
            name="employee-no"
            id="employee-no"
            class="form-control"
          />
        </div>
        <div class="form-group mt-3">
          <label for="name">Name:</label>
          <input
            type="text"
            name="name"
            id="name"
            class="form-control"
          />
        </div>
        <button type="submit" class="btn btn-success mt-3" name="submit-p">
          Submit
        </button>
      </form>
    </div>
  </div>

  <div class="form-group mt-3">
    <button
      class="btn btn-secondary"
      onclick="location.href='{{ url_for('ui') }}';"
    >
      Back
    </button>
  </div>
</div>

<div 
  id="data-container"
  data-units-by-department='{{ units_by_department | tojson | safe }}'
  data-tests-overview='{{ tests_overview | tojson | safe }}'
></div>


<script type="module" src="{{ url_for('static', filename='js/view.js') }}"></script>

{% endblock %}
